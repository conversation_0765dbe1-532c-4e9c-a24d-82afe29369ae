import logging

from django.db.models.signals import post_save
from django.dispatch import receiver

from knowledge_bases.models import IngestionJob
from knowledge_bases.tasks import process_ingestion_job

logger = logging.getLogger(__name__)


@receiver(post_save, sender=IngestionJob)
def trigger_ingestion_job(sender, instance, created, **kwargs):
    """
    Signal handler to trigger Celery task when an IngestionJob is created.
    """
    logger.info("IngestionJob signal received for ID %s", instance.id)
    if created:  # Only trigger on creation, not updates
        # Trigger the Celery task
        logger.info("Triggering Celery task for IngestionJob with ID %s", instance.id)
        process_ingestion_job.delay(instance.id)
