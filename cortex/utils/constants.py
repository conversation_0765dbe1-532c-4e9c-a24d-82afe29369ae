from django.conf import settings

ENV = settings.ENV

CLAUDE_V2_MODEL_ID = ENV.str("CLAUDE_V2_MODEL_ID", default="anthropic.claude-v2")
CLAUDE_V2_1_MODEL_ID = ENV.str("CLAUDE_V2_1_M<PERSON>EL_ID", default="anthropic.claude-v2:1")
CLAUDE_INSTANT_V1 = ENV.str("CLAUDE_INSTANT_V1", default="anthropic.claude-instant-v1")
CLAUDE_V3_SONNET_MODEL_ID = ENV.str("CLAUDE_V3_SONNET_MODEL_ID", default="anthropic.claude-3-sonnet-20240229-v1:0")
CLAUDE_V3_5_SONNET_MODEL_ID = ENV.str(
    "CLAUDE_V3_5_SONNET_MODEL_ID", default="anthropic.claude-3-5-sonnet-20240620-v1:0"
)
CLAUDE_V3_HAIKU_MODEL_ID = ENV.str("CLAUDE_V3_HAIKU_MODEL_ID", default="anthropic.claude-3-haiku-20240307-v1:0")
CLAUDE_V3_5_HAIKU_MODEL_ID = ENV.str("CLAUDE_V3_5_HAIKU_MODEL_ID", default="anthropic.claude-3-5-haiku-20241022-v1:0")

DEFAULT_CLAUDE_MAX_TOKENS_TO_SAMPLE = ENV.int("DEFAULT_CLAUDE_MAX_TOKENS_TO_SAMPLE", default=6000)

NOVA_PRO_MODEL_ID = ENV.str("NOVA_PRO_MODEL_ID", default="us.amazon.nova-pro-v1:0")

COHERE_EMBEDDING_MODEL_ID = ENV.str("COHERE_EMBEDDING_MODEL_ID", default="cohere.embed-multilingual-v3")
COHERE_EMBEDDING_MODEL_OUTPUT_DIM = ENV.int("COHERE_EMBEDDING_MODEL_OUTPUT_DIM", default=1024)
COHERE_RERANK_MODEL_ID = ENV.str("COHERE_RERANK_MODEL_ID", default="cohere.rerank-v3-5:0")

OPENSEARCH_INDEX_NAME = ENV.str("OPENSEARCH_INDEX_NAME", default="cortex")
POSTGRES_DOCSTORE_TABLE_NAME = ENV.str("DOCSTORE_TABLE_NAME", default="sources_docstore")
