class BlockKitBuilder:
    @staticmethod
    def section_block(block_id, show=True, elements=None, accessory=None, variant=None, url=None):
        """
        Create a section block containing other block elements
        """
        section = {
            "type": "section",
            "elements": elements or [],
            "id": block_id,
            "show": show,
            "accessory": accessory,
        }
        if variant:
            section["variant"] = variant

        if url:
            section["url"] = url

        return section

    @staticmethod
    def accessory_block(type: str, image_url: str, alt_text: str):
        """
        Create an actions block containing interactive elements like buttons
        """
        return {
            "type": type,
            "url": image_url,
            "alt": alt_text,
        }

    @staticmethod
    def context_block(block_id, text, variant=None, show=True):
        """
        Create a context block containing text elements
        """
        context_block = {
            "type": "context",
            "text": text,
            "id": block_id,
            "show": show,
        }
        if variant:
            context_block["variant"] = variant

        return context_block

    @staticmethod
    def actions_block(block_id, show=True, elements=None):
        """
        Create an actions block containing interactive elements like buttons
        """
        return {
            "type": "actions",
            "elements": elements or [],
            "show": show,
            "id": block_id,
        }

    @staticmethod
    def divider_block(block_id, show=True):
        """
        Create a divider block
        """
        return {"type": "divider", "id": block_id, "show": show}

    @staticmethod
    def form_block(block_id, show=True, fields=None):
        """
        Create a form block with input fields
        """
        return {
            "type": "form",
            "fields": fields or [],
            "id": block_id,
            "show": show,
        }

    @staticmethod
    def markdown_block(block_id, text, show=True):
        """
        Create a markdown block with the given text
        """
        return {
            "type": "markdown",
            "text": text,
            "id": block_id,
            "show": show,
        }

    @staticmethod
    def button_block(text, button_id, variant="primary", show=True, url=None, action=None, icon=None):
        """
        Create a button block with the given parameters
        Args:
            text: Button text
            button_id: Unique identifier for the button
            show: Boolean to control button visibility
            url: URL to open when clicked (for open_url action)
            action: Action type (open_url or post_message)
            icon: Icon to display on the button
        """
        button = {
            "type": "button",
            "show": show,
            "text": text,
            "id": button_id,
            "variant": variant,
            "icon": icon or "",
        }

        if url:
            button["url"] = url
        if action:
            button["action"] = action

        return button

    @staticmethod
    def input_block(block_id, name, label, input_type="text", required=False, validation=None):
        """
        Create an input field block
        Args:
            name: Field name/identifier
            label: Display label
            input_type: Type of input (text/textarea)
            required: Whether field is required
            validation: Validation rules dictionary
        """
        return {
            "name": name,
            "label": label,
            "type": input_type,
            "required": required,
            "validation": validation or {},
            "id": block_id,
        }

    @staticmethod
    def text_input_block(block_id, name, label, required=False, pattern=None, message=None):
        """
        Create a text input field with pattern validation
        """
        validation = {"pattern": pattern, "message": message} if pattern else None
        return BlockKitBuilder.input_block(
            block_id=block_id,
            name=name,
            label=label,
            input_type="text",
            required=required,
            validation=validation,
        )

    @staticmethod
    def textarea_block(block_id, name, label, required=False, min_length=None, message=None):
        """
        Create a textarea input field with length validation
        """
        validation = {"min_length": min_length, "message": message} if min_length else None
        return BlockKitBuilder.input_block(
            block_id=block_id,
            name=name,
            label=label,
            input_type="textarea",
            required=required,
            validation=validation,
        )

    @staticmethod
    def select_block(block_id, name, label, options, required=False):
        """
        Create a select input field with options
        """
        return {
            "id": block_id,
            "name": name,
            "label": label,
            "type": "select",
            "options": options,
            "required": required,
        }

    @staticmethod
    def select_option(value, label):
        """
        Create an option for select input field
        """
        return {"value": value, "label": label}

    # Helper methods for common input fields
    @staticmethod
    def name_input(block_id):
        """Create a standard name input field"""
        return BlockKitBuilder.text_input_block(
            block_id,
            name="name",
            label="Name",
            required=True,
            pattern="^[a-zA-Z\\s]{2,50}$",
            message="Please enter a valid name",
        )

    @staticmethod
    def email_input(block_id):
        """Create a standard email input field"""
        return BlockKitBuilder.text_input_block(
            block_id=block_id,
            name="email",
            label="Email",
            required=True,
            pattern=r"^(?=.{1,200}$)[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$",
            message="Please enter a valid email",
        )

    @staticmethod
    def message_input(block_id):
        """Create a standard message textarea field"""
        return BlockKitBuilder.textarea_block(
            block_id=block_id,
            name="message",
            label="Message",
            required=True,
            min_length=10,
            message="Message must be at least 10 characters",
        )
