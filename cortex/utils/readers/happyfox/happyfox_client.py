import aiohttp
import html2text
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class HappyFoxStaffAuthClient:
    """Staff login authentication client for HappyFox API with optional app context."""

    def __init__(self, username: str, password: str, domain: str, app_id: Optional[str] = None, client_id: Optional[str] = None):
        self.username = username
        self.password = password
        self.domain = domain
        self.app_id = app_id
        self.client_id = client_id
        self.access_token = None
        self.token_expires_at = None
        self.token_type = "Token"

    async def get_access_token(self) -> str:
        """Get access token using staff login."""
        from datetime import timezone
        current_time = datetime.now(timezone.utc)

        # Check if current token is still valid (tokens typically last 24 hours)
        if self.access_token and self.token_expires_at:
            # Ensure both datetimes are timezone-aware for comparison
            expires_at = self.token_expires_at
            if expires_at.tzinfo is None:
                expires_at = expires_at.replace(tzinfo=timezone.utc)

            if expires_at > current_time:
                logger.debug("Using cached staff login token")
                return self.access_token

        logger.info("Requesting new staff login token")
        await self._staff_login()
        return self.access_token

    async def _staff_login(self):
        """Perform staff login to get access token with optional app context."""
        login_url = f"{self.domain}/api/v2/staff-login/"

        # Prepare login data
        login_data = {
            "username": self.username,
            "password": self.password,
            "remember_me": True
        }

        # Add app context if available
        if self.app_id:
            login_data["app_id"] = self.app_id
        if self.client_id:
            login_data["client_id"] = self.client_id

        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        # Add app context headers if available
        if self.app_id:
            headers['X-HappyFox-App-ID'] = self.app_id
        if self.client_id:
            headers['X-HappyFox-Client-ID'] = self.client_id

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.post(login_url, json=login_data, headers=headers) as response:
                    if response.status == 200:
                        # Check content type before trying to parse as JSON
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            login_response = await response.json()

                            # Extract token from response (HappyFox uses 'auth_token' key)
                            if 'auth_token' in login_response:
                                self.access_token = login_response['auth_token']
                                self.token_type = "Token"

                                # Use the provided expiration time if available
                                expires_at_str = login_response.get('expires_at')
                                if expires_at_str:
                                    try:
                                        from datetime import datetime
                                        # Parse the ISO format timestamp
                                        self.token_expires_at = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
                                        logger.info(f"Token expires at: {self.token_expires_at}")
                                    except Exception as e:
                                        logger.warning(f"Could not parse expiration time: {e}, using default")
                                        from datetime import timezone
                                        self.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=23)
                                else:
                                    # Set expiration time (default to 24 hours for staff tokens)
                                    from datetime import timezone
                                    self.token_expires_at = datetime.now(timezone.utc) + timedelta(hours=23)  # 1 hour buffer

                                logger.info("Staff login successful, token obtained")
                                logger.info(f"Token: {self.access_token[:20]}...")
                            else:
                                logger.error(f"No auth_token found in login response: {login_response}")
                                raise Exception("Staff login response did not contain an auth_token")
                        else:
                            # Response is not JSON, likely an error page
                            error_text = await response.text()
                            logger.error(f"Staff login returned HTML instead of JSON. Content-Type: {content_type}")
                            logger.error(f"Response content (first 500 chars): {error_text[:500]}")
                            raise Exception("Staff login endpoint returned HTML instead of JSON")
                    else:
                        error_text = await response.text()
                        logger.error(f"Staff login failed: {response.status} - {error_text}")
                        raise Exception(f"Staff login failed: {response.status} - {error_text}")

        except Exception as e:
            logger.error(f"Error during staff login: {e}")
            raise Exception(f"Failed to perform staff login: {e}")

    async def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers with staff login token and app context."""
        access_token = await self.get_access_token()
        headers = {
            "Authorization": f"{self.token_type} {access_token}",
            "Content-Type": "application/json"
        }

        # Add app context headers if available
        if self.app_id:
            headers['X-HappyFox-App-ID'] = self.app_id
        if self.client_id:
            headers['X-HappyFox-Client-ID'] = self.client_id

        return headers

class HappyFoxClient:
    def __init__(self, domain: str, api_key: Optional[str] = None, auth_code: Optional[str] = None,
                 staff_credentials: Optional[Dict[str, str]] = None, oauth_credentials: Optional[Dict[str, str]] = None,
                 oauth_token: Optional[str] = None):
        """
        Initialize HappyFox client with API key + auth code, staff login, or app credentials.

        Args:
            domain: HappyFox domain URL
            api_key: HappyFox API key
            auth_code: HappyFox auth code (required with api_key)
            staff_credentials: Dict with keys: username, password, app_id (optional)
            oauth_credentials: Dict with keys: client_id, client_secret, signing_secret
        """
        self.domain = domain
        self.base_url = f"{domain}/api/v2"
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.last_request_time = None
        self.rate_limit_delay = 1.0
        self.max_retries = 3

        # Authentication setup - Priority: OAuth Token > API Key + Auth Code > Staff Login > App Auth
        if oauth_token:
            # Direct OAuth access token (from authorization code flow)
            self.oauth_token = oauth_token
            self.staff_auth_client = None
            self.app_auth_client = None
            self.api_key = None
            self.auth_code = None
            self.auth_method = 'oauth_token'
            logger.info("HappyFoxClient initialized with OAuth access token")
        elif api_key and auth_code:
            self.api_key = api_key
            self.auth_code = auth_code
            self.staff_auth_client = None
            self.app_auth_client = None
            self.auth_method = 'api_key'
            logger.info("HappyFoxClient initialized with API key + auth code")
        elif staff_credentials:
            self.staff_auth_client = HappyFoxStaffAuthClient(
                username=staff_credentials['username'],
                password=staff_credentials['password'],
                domain=domain,
                app_id=staff_credentials.get('app_id'),
                client_id=staff_credentials.get('client_id')
            )
            self.app_auth_client = None
            self.api_key = None
            self.auth_code = None
            self.auth_method = 'staff'
            logger.info("HappyFoxClient initialized with staff login credentials")
        elif oauth_credentials:
            # Use staff login with app context (hybrid approach)
            import os
            # Get staff credentials from environment
            username = os.getenv('HAPPYFOX_USERNAME')
            password = os.getenv('HAPPYFOX_PASSWORD')
            app_id = os.getenv('HAPPYFOX_APP_ID')

            if username and password:
                self.staff_auth_client = HappyFoxStaffAuthClient(
                    username=username,
                    password=password,
                    domain=domain,
                    app_id=app_id,
                    client_id=oauth_credentials['client_id']  # Use OAuth client_id for app context
                )
                self.app_auth_client = None
                self.api_key = None
                self.auth_code = None
                self.auth_method = 'staff_with_app_context'
                logger.info("HappyFoxClient initialized with staff credentials + app context")
            else:
                raise ValueError("OAuth credentials provided but no staff credentials found in environment. "
                               "Please use OAuth token authentication via admin interface.")
        else:
            raise ValueError("Either api_key + auth_code, staff_credentials, or oauth_credentials must be provided")

    async def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers based on configured auth method."""
        if self.auth_method == 'oauth_token':
            # Use OAuth access token (from authorization code flow)
            # HappyFox uses "Token" format, not "Bearer"
            return {
                "Authorization": f"Token {self.oauth_token}",
                "Content-Type": "application/json"
            }
        elif self.auth_method == 'api_key':
            # Use Basic HTTP authentication with API key and auth code
            import base64
            credentials = base64.b64encode(f"{self.api_key}:{self.auth_code}".encode()).decode()
            return {
                "Authorization": f"Basic {credentials}",
                "Content-Type": "application/json"
            }
        elif self.auth_method in ['staff', 'staff_with_app_context']:
            return await self.staff_auth_client.get_auth_headers()

        else:
            raise ValueError(f"Unknown authentication method: {self.auth_method}")

    async def _rate_limit(self):
        """Implement simple rate limiting to avoid overwhelming the API."""
        from datetime import timezone
        current_time = datetime.now(timezone.utc)

        if self.last_request_time:
            # Ensure both datetimes are timezone-aware for comparison
            if self.last_request_time.tzinfo is None:
                self.last_request_time = self.last_request_time.replace(tzinfo=timezone.utc)

            elapsed = current_time - self.last_request_time
            if elapsed < timedelta(seconds=self.rate_limit_delay):
                sleep_time = self.rate_limit_delay - elapsed.total_seconds()
                await asyncio.sleep(sleep_time)
        self.last_request_time = current_time

    async def _make_request(self, url: str, headers: Dict[str, str]) -> Dict[str, Any]:
        """Make an HTTP request with retry logic and rate limiting."""
        await self._rate_limit()

        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            for attempt in range(self.max_retries):
                try:
                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            return await response.json()
                        elif response.status == 429: 
                            retry_after = int(response.headers.get('Retry-After', 60))
                            logger.warning(f"Rate limited. Waiting {retry_after} seconds before retry {attempt + 1}/{self.max_retries}")
                            await asyncio.sleep(retry_after)
                        elif response.status in [502, 503, 504]: 
                            wait_time = 2 ** attempt  
                            logger.warning(f"Server error {response.status}. Retrying in {wait_time}s (attempt {attempt + 1}/{self.max_retries})")
                            await asyncio.sleep(wait_time)
                        else:
                            error_text = await response.text()
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"HTTP {response.status}: {error_text}"
                            )
                except aiohttp.ClientError as e:
                    if attempt == self.max_retries - 1:
                        raise
                    wait_time = 2 ** attempt
                    logger.warning(f"Request failed: {e}. Retrying in {wait_time}s (attempt {attempt + 1}/{self.max_retries})")
                    await asyncio.sleep(wait_time)

        raise Exception(f"Failed to make request to {url} after {self.max_retries} attempts")

    async def fetch_articles(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """Fetch paginated articles with improved error handling."""
        url = f"{self.base_url}/kb/articles?page={page}&per_page={per_page}&include_attachments=true"
        headers = await self._get_auth_headers()

        logger.debug(f"Fetching articles page {page} (per_page={per_page})")
        try:
            response = await self._make_request(url, headers)
            logger.debug(f"Successfully fetched page {page} with {len(response.get('results', []))} articles")
            return response
        except Exception as e:
            logger.error(f"Failed to fetch articles page {page}: {e}")
            raise

    async def fetch_article_details(self, article_id: int):
        """Fetch and convert a single article to Markdown."""
        url = f"{self.base_url}/kb/articles/{article_id}"
        headers = await self._get_auth_headers()

        try:
            response = await self._make_request(url, headers)
            html_content = response.get("contents", "")
            markdown = self.html_converter.handle(html_content).strip()

            # Get attachments from article response or fetch separately
            attachments = response.get("attachments", [])
            if not attachments:
                try:
                    attachments = await self.fetch_article_attachments(article_id)
                except Exception as e:
                    logger.debug(f"Could not fetch attachments for article {article_id}: {e}")
                    attachments = []

            return {
                "text": markdown,
                "url": response.get("public_url", ""),
                "attachments": attachments
            }
        except Exception as e:
            logger.error(f"Failed to fetch article {article_id}: {e}")
            raise

    async def fetch_article_attachments(self, article_id: int) -> List[dict]:
        """Fetch attachments for a specific article."""
        # Try a few common attachment endpoints
        endpoints = [
            f"/kb/articles/{article_id}/attachments",
            f"/kb/attachments?article_id={article_id}",
        ]

        headers = await self._get_auth_headers()

        for endpoint in endpoints:
            try:
                url = f"{self.base_url}{endpoint}"
                response = await self._make_request(url, headers)

                if response:
                    if isinstance(response, list):
                        return response
                    elif isinstance(response, dict):
                        return response.get("results", response.get("attachments", []))

            except Exception as e:
                logger.debug(f"Attachment endpoint {endpoint} failed: {e}")
                continue

        return []



    async def download_attachment(self, attachment_url: str, local_path: str) -> bool:
        """Download an attachment from HappyFox to local storage."""
        try:
            # Use auth headers for internal URLs, simple headers for external (S3, etc.)
            if attachment_url.startswith(self.domain):
                headers = await self._get_auth_headers()
            else:
                headers = {"User-Agent": "HappyFox-Reader/1.0"}

            await self._rate_limit()

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                async with session.get(attachment_url, headers=headers) as response:
                    if response.status == 200:
                        import os
                        os.makedirs(os.path.dirname(local_path), exist_ok=True)

                        with open(local_path, 'wb') as f:
                            async for chunk in response.content.iter_chunked(8192):
                                f.write(chunk)

                        logger.info(f"Downloaded: {os.path.basename(local_path)}")
                        return True
                    else:
                        logger.error(f"Download failed: HTTP {response.status}")
                        return False

        except Exception as e:
            logger.error(f"Download error: {e}")
            return False

    async def download_attachment_to_memory(self, attachment_url: str) -> bytes:
        """Download an attachment from HappyFox to memory and return the bytes."""
        try:
            # Use auth headers for internal URLs, simple headers for external (S3, etc.)
            if attachment_url.startswith(self.domain):
                headers = await self._get_auth_headers()
            else:
                headers = {"User-Agent": "HappyFox-Reader/1.0"}

            await self._rate_limit()

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=60)) as session:
                async with session.get(attachment_url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.read()
                        logger.info(f"Downloaded to memory: {len(content)} bytes")
                        return content
                    else:
                        logger.error(f"Download failed: HTTP {response.status}")
                        return b""

        except Exception as e:
            logger.error(f"Download to memory error: {e}")
            return b""