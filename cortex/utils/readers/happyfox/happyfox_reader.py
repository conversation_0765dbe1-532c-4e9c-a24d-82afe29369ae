import logging
import hashlib
import os
import aiofiles
import json
import asyncio
import mimetypes
from typing import List, Optional, Dict, Any
from llama_index.core.schema import Document
from llama_index.core.readers.base import BaseReader
from .happyfox_client import HappyFoxClient
from .utils import get_happyfox_credentials

logger = logging.getLogger(__name__)

CACHE_DIR = "./knowledge_base_indexes/cache/happyfox"
os.makedirs(CACHE_DIR, exist_ok=True)

def compute_hash(article: dict) -> str:
    """Generate a hash for an article based on last_updated_at and id"""
    data = f"{article['id']}_{article['last_updated_at']}"
    return hashlib.md5(data.encode("utf-8")).hexdigest()

async def load_cache(hash_key: str) -> Optional[Document]:
    cache_path = os.path.join(CACHE_DIR, f"{hash_key}.json")
    if os.path.exists(cache_path):
        async with aiofiles.open(cache_path, "r") as f:
            content = await f.read()
            data = json.loads(content)
            return Document(**data)
    return None

async def save_cache(hash_key: str, doc: Document):
    cache_path = os.path.join(CACHE_DIR, f"{hash_key}.json")
    async with aiofiles.open(cache_path, "w") as f:
        await f.write(doc.json())
    logger.info(f"Saved cache for {doc.metadata['title']}")




class HappyFoxKnowledgeBaseReader(BaseReader):
    def __init__(self, sections: Optional[List[int]] = None, api_key: Optional[str] = None, auth_code: Optional[str] = None,
                 domain: Optional[str] = None, staff_credentials: Optional[Dict[str, str]] = None,
                 oauth_credentials: Optional[Dict[str, str]] = None, oauth_token: Optional[str] = None,
                 process_attachments: bool = True, attachment_download_dir: Optional[str] = None):

        # Initialize HappyFox client with provided credentials
        if oauth_token and domain:
            logger.info(f"Using OAuth access token: domain={domain}")
            self.client = HappyFoxClient(domain=domain, oauth_token=oauth_token)
        elif staff_credentials and domain:
            logger.info(f"Using staff credentials: domain={domain}, username={staff_credentials.get('username', 'N/A')}")
            self.client = HappyFoxClient(domain=domain, staff_credentials=staff_credentials)
        elif api_key and auth_code and domain:
            logger.info(f"Using API key authentication: domain={domain}")
            self.client = HappyFoxClient(domain=domain, api_key=api_key, auth_code=auth_code)
        elif oauth_credentials and domain:
            logger.info(f"Using OAuth credentials: domain={domain}")
            self.client = HappyFoxClient(domain=domain, oauth_credentials=oauth_credentials)
        else:
            # Fallback to environment variables
            logger.info("No credentials provided, checking environment variables")
            try:
                api_key, domain, staff_creds, oauth_creds, auth_code = get_happyfox_credentials()
                if oauth_creds:
                    self.client = HappyFoxClient(domain=domain, oauth_credentials=oauth_creds)
                elif staff_creds:
                    self.client = HappyFoxClient(domain=domain, staff_credentials=staff_creds)
                elif api_key and auth_code:
                    self.client = HappyFoxClient(domain=domain, api_key=api_key, auth_code=auth_code)
                else:
                    raise ValueError("No valid credentials found")
            except Exception as e:
                raise ValueError(f"No valid HappyFox credentials found: {e}")

        self.sections = sections or []
        self.process_attachments = process_attachments
        self.attachment_download_dir = attachment_download_dir or os.path.join(CACHE_DIR, "attachments")
        os.makedirs(self.attachment_download_dir, exist_ok=True)

    async def fetch_all_articles(self) -> List[dict]:
        """Fetch all articles from HappyFox with pagination."""
        logger.info("Fetching articles from HappyFox...")
        all_articles = []
        page = 1

        while True:
            logger.info(f"Fetching page {page} of articles...")
            response = await self.client.fetch_articles(page=page, per_page=50)
            results = response.get("results", [])

            if not results:
                break

        
            if self.sections:
                filtered_results = []
                for article in results:
                    section_id = article.get("section", {}).get("id")
                    if section_id in self.sections:
                        filtered_results.append(article)
                results = filtered_results

            all_articles.extend(results)

          
            if not response.get("meta", {}).get("next"):
                break

            page += 1

        logger.info(f"Fetched {len(all_articles)} articles from HappyFox")
        return all_articles

    async def fetch_article_batch(self, articles: List[dict]) -> List[Document]:
        logger.info(f"Processing {len(articles)} articles...")

        tasks = [self.process_article(article) for article in articles]
        results = await asyncio.gather(*tasks)

        documents = []
        for result in results:
            if isinstance(result, list):
                documents.extend(result)
            elif isinstance(result, Document):
                documents.append(result)

        return documents

    async def process_article(self, article: dict) -> List[Document]:
        """Process an article and its attachments, returning a list of documents."""
        logger.info(f"Processing article ID {article['id']}")

        # TEMPORARILY DISABLE CACHE TO FORCE IMAGE EXTRACTION
        # hash_key = compute_hash(article)
        # cached_doc = await load_cache(hash_key)
        # if cached_doc:
        #     logger.info(f"Using cached article: {article['title']}")
        #     # Still process attachments for cached articles
        #     attachments = article.get("attachments", [])
        #     if self.process_attachments and attachments:
        #         try:
        #             attachment_docs = await self.process_attachments_for_article(article, attachments)
        #             return [cached_doc] + attachment_docs
        #         except Exception as e:
        #             logger.error(f"Attachment processing failed: {e}")
        #             return [cached_doc]
        #     return [cached_doc]

        documents = []

        try:
            detail = await self.client.fetch_article_details(article["id"])
            content = detail.get("text", "")
            url = detail.get("url", "")

            # Get attachments from article or detail response
            attachments = article.get("attachments", []) or detail.get("attachments", [])

            # Extract images from content and add them as attachments
            import re
            print(f"🔍 CHECKING ARTICLE {article['id']} FOR IMAGES")
            print(f"🔍 Content length: {len(content)} characters")
            print(f"🔍 Content preview: {content[:200]}...")

            # Check for both HTML img tags and Markdown images
            html_img_urls = re.findall(r'<img[^>]*src=["\']([^"\']+)["\'][^>]*>', content, re.IGNORECASE)
            markdown_img_urls = re.findall(r'!\[[^\]]*\]\(([^)]+)\)', content)

            img_urls = html_img_urls + markdown_img_urls
            print(f"🔍 Found {len(html_img_urls)} HTML img tags, {len(markdown_img_urls)} Markdown images")
            print(f"🔍 Total images: {len(img_urls)}")

            if img_urls:
                print(f"🔍 Article {article['id']} found {len(img_urls)} embedded images in content")
                logger.info(f"Article {article['id']} found {len(img_urls)} embedded images in content")
                for i, img_url in enumerate(img_urls):
                    # Create a pseudo-attachment for the embedded image
                    img_name = f"embedded_image_{i+1}.png"
                    img_attachment = {
                        'id': f"img_{article['id']}_{i+1}",
                        'name': img_name,
                        'url': img_url,
                        'embedded': True  # Mark as embedded image
                    }
                    attachments.append(img_attachment)
                    print(f"🔍   Added embedded image: {img_name} - {img_url}")
                    logger.info(f"  Added embedded image: {img_name} - {img_url}")
            else:
                print(f"🔍 No images found in article {article['id']}")

            logger.info(f"Article {article['id']} has {len(attachments)} total attachments ({len(img_urls)} embedded images)")

            # Create main article document
            main_doc = Document(
                text=content,
                metadata={
                    "source": "HappyFox",
                    "content_type": "article",
                    "title": article.get("title"),
                    "url": url,
                    "article_id": article.get("id"),
                    "section_id": article.get("section", {}).get("id"),
                    "last_updated_at": article.get("last_updated_at"),
                    "has_attachments": len(attachments) > 0,
                    "attachment_count": len(attachments)
                }
            )
            documents.append(main_doc)

            if self.process_attachments and attachments:
                try:
                    attachment_docs = await self.process_attachments_for_article(article, attachments)
                    documents.extend(attachment_docs)
                except Exception as e:
                    logger.error(f"Attachment processing failed for article {article['id']}: {e}")
                    # Create simple placeholder documents for failed attachments
                    for attachment in attachments:
                        simple_doc = Document(
                            text=f"Attachment: {attachment.get('name', 'unknown')}\nProcessing failed: {str(e)}",
                            metadata={
                                "source": "HappyFox",
                                "content_type": "attachment",
                                "attachment_name": attachment.get('name', 'unknown'),
                                "parent_article_id": article.get("id"),
                                "error": str(e)
                            }
                        )
                        documents.append(simple_doc)

            # await save_cache(hash_key, main_doc)  # Disabled with cache

            return documents

        except Exception as e:
            logger.error(f"Failed to process article {article['id']}: {e}")
            return []

    async def process_attachments_for_article(self, article: dict, attachments: List[dict]) -> List[Document]:
        """Process all attachments for an article."""
        attachment_docs = []
        logger.info(f"Processing {len(attachments)} attachments for article {article['id']}")

        for i, attachment in enumerate(attachments):
            attachment_id = attachment.get('id', 'unknown')
            attachment_name = attachment.get('name', f'attachment_{attachment_id}')
            logger.info(f"Processing attachment {i+1}/{len(attachments)}: {attachment_name} (ID: {attachment_id})")

            try:
                attachment_doc = await self.process_single_attachment(article, attachment)
                if attachment_doc:
                    attachment_docs.append(attachment_doc)
                    logger.info(f"Successfully processed attachment: {attachment_name}")
                else:
                    logger.warning(f"No document created for attachment: {attachment_name}")
            except Exception as e:
                logger.error(f"Error processing attachment {attachment_id} ({attachment_name}): {e}")
                continue

        logger.info(f"Completed processing {len(attachment_docs)} out of {len(attachments)} attachments for article {article['id']}")
        return attachment_docs

    def _create_django_document_for_article(self, article: dict):
        """Create a Django document record for an article if data_source is available."""
        if not self.data_source:
            return None

        try:
            from knowledge_bases.models import Document as DjangoDocument

            django_doc, created = DjangoDocument.objects.get_or_create(
                data_source=self.data_source,
                title=article.get("title", f"Article {article.get('id')}"),
                defaults={
                    'raw_content': article.get("text", ""),
                    'metadata': {
                        "source": "HappyFox",
                        "article_id": article.get("id"),
                        "section_id": article.get("section", {}).get("id"),
                        "last_updated_at": article.get("last_updated_at"),
                        "url": article.get("url", ""),
                    }
                }
            )

            if created:
                logger.info(f"Created Django document for article {article.get('id')}")

            return django_doc

        except Exception as e:
            logger.error(f"Failed to create Django document for article {article.get('id')}: {e}")
            return None

    async def process_single_attachment(self, article: dict, attachment: dict) -> Optional[Document]:
        """Process a single attachment (file attachment or embedded image)."""
        attachment_id = attachment.get("id")
        attachment_name = attachment.get("name", f"attachment_{attachment_id}")
        attachment_url = attachment.get("url", "")
        is_embedded = attachment.get("embedded", False)

        logger.info(f"Processing {'embedded image' if is_embedded else 'attachment'} {attachment_id}: {attachment_name}")

        if not attachment_url:
            logger.warning(f"No URL found for attachment {attachment_id}")
            return None

        # Determine file extension and MIME type
        file_extension = os.path.splitext(attachment_name)[1].lower()
        mime_type, _ = mimetypes.guess_type(attachment_name)

        # For embedded images, try to guess MIME type from URL
        if is_embedded and not mime_type:
            if any(ext in attachment_url.lower() for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                if '.png' in attachment_url.lower():
                    mime_type = "image/png"
                elif any(ext in attachment_url.lower() for ext in ['.jpg', '.jpeg']):
                    mime_type = "image/jpeg"
                elif '.gif' in attachment_url.lower():
                    mime_type = "image/gif"
                elif '.webp' in attachment_url.lower():
                    mime_type = "image/webp"
                else:
                    mime_type = "image/png"  # Default for images
            else:
                mime_type = "image/png"  # Default for embedded images

        if not mime_type:
            mime_type = "application/octet-stream"

        # Create local file path
        safe_filename = f"article_{article['id']}_{'image' if is_embedded else 'attachment'}_{attachment_id}_{attachment_name}"
        local_path = os.path.join(self.attachment_download_dir, safe_filename)

        # Download attachment/image
        download_success = await self.client.download_attachment(attachment_url, local_path)
        if not download_success:
            return Document(
                text=f"{'Embedded Image' if is_embedded else 'Attachment'}: {attachment_name}\nFile Type: {mime_type}\nURL: {attachment_url}\n\nDownload failed but {'image' if is_embedded else 'attachment'} detected.",
                metadata={
                    "source": "HappyFox",
                    "content_type": "embedded_image" if is_embedded else "attachment",
                    "parent_article_id": article.get("id"),
                    "parent_article_title": article.get("title"),
                    "attachment_id": attachment_id,
                    "attachment_name": attachment_name,
                    "attachment_url": attachment_url,
                    "mime_type": mime_type,
                    "file_extension": file_extension,
                    "local_path": local_path,
                    "section_id": article.get("section", {}).get("id"),
                    "download_failed": True,
                    "is_embedded_image": is_embedded
                }
            )

        # Create document for successfully downloaded attachment/image
        file_size = os.path.getsize(local_path) if os.path.exists(local_path) else 0
        return Document(
            text=f"{'Embedded Image' if is_embedded else 'Attachment'}: {attachment_name}\nFile Type: {mime_type}\nSize: {file_size} bytes\n\nReady for PremiumParser processing.",
            metadata={
                "source": "HappyFox",
                "content_type": "embedded_image" if is_embedded else "attachment",
                "parent_article_id": article.get("id"),
                "parent_article_title": article.get("title"),
                "attachment_id": attachment_id,
                "attachment_name": attachment_name,
                "attachment_url": attachment_url,
                "mime_type": mime_type,
                "file_extension": file_extension,
                "file_size": file_size,
                "local_path": local_path,
                "section_id": article.get("section", {}).get("id"),
                "is_embedded_image": is_embedded
            }
        )



    async def load_data(self) -> List[Document]:
        logger.info("Starting HappyFox knowledge base ingestion")
        articles = await self.fetch_all_articles()
        logger.info(f"Fetched {len(articles)} articles from HappyFox")

        # Log attachment summary (including embedded images)
        articles_with_attachments = [a for a in articles if a.get("attachments")]
        logger.info(f"Found {len(articles_with_attachments)} articles with attachments")

        # Count file attachments vs embedded images
        total_attachments = 0
        total_embedded_images = 0
        for article in articles_with_attachments:
            attachments = article.get("attachments", [])
            for att in attachments:
                if att.get("embedded", False):
                    total_embedded_images += 1
                else:
                    total_attachments += 1

        logger.info(f"Total file attachments: {total_attachments}")
        logger.info(f"Total embedded images: {total_embedded_images}")
        logger.info(f"Total attachments + images: {total_attachments + total_embedded_images}")

        # Show which articles have attachments/images
        for article in articles_with_attachments:
            attachments = article.get("attachments", [])
            file_attachments = [a for a in attachments if not a.get("embedded", False)]
            embedded_images = [a for a in attachments if a.get("embedded", False)]

            logger.info(f"Article {article['id']} ({article.get('title', 'No title')[:30]}...) has {len(file_attachments)} file attachments, {len(embedded_images)} embedded images")

            for att in file_attachments:
                logger.info(f"  File: {att.get('name', 'unnamed')} ({att.get('url', 'no url')})")
            for img in embedded_images:
                logger.info(f"  Image: {img.get('name', 'unnamed')} ({img.get('url', 'no url')})")

        documents = await self.fetch_article_batch(articles)

        # Count articles and attachments
        article_count = sum(1 for doc in documents if doc.metadata.get("content_type") == "article")
        attachment_count = sum(1 for doc in documents if doc.metadata.get("content_type") == "attachment")

        logger.info(f"Successfully processed {article_count} articles and {attachment_count} attachments")
        logger.info(f"Total documents: {len(documents)}")

        return documents


