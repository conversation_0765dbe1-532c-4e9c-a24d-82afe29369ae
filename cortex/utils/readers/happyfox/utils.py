from dotenv import load_dotenv
import os

project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
env_path = os.path.join(project_root, ".env")

load_dotenv(dotenv_path=env_path)

def get_happyfox_credentials():
    """
    Get HappyFox credentials from environment variables for fallback support.
    Note: OAuth token authentication is preferred and handled via admin interface.
    """
    # Try OAuth credentials first
    client_id = os.getenv("HAPPYFOX_CLIENT_ID")
    client_secret = os.getenv("HAPPYFOX_CLIENT_SECRET")
    signing_secret = os.getenv("HAPPYFOX_SIGNING_SECRET")
    domain = os.getenv("HAPPYFOX_DOMAIN")

    if all([client_id, client_secret, signing_secret, domain]):
        oauth_credentials = {
            'client_id': client_id,
            'client_secret': client_secret,
            'signing_secret': signing_secret
        }
        return None, domain, None, oauth_credentials, None

    # Try staff credentials as fallback
    username = os.getenv("HAPPYFOX_USERNAME")
    password = os.getenv("HAPPYFOX_PASSWORD")
    app_id = os.getenv("HAPPYFOX_APP_ID")

    if all([username, password, domain]):
        staff_credentials = {
            'username': username,
            'password': password,
            'app_id': app_id
        }
        return None, domain, staff_credentials, None, None

    # Try API key credentials as last resort
    api_key = os.getenv("HAPPYFOX_API_KEY")
    auth_code = os.getenv("HAPPYFOX_AUTH_CODE")

    if all([api_key, auth_code, domain]):
        return api_key, domain, None, None, auth_code

    raise ValueError("No valid HappyFox credentials found in environment variables. "
                     "Please use OAuth token authentication via the admin interface.")