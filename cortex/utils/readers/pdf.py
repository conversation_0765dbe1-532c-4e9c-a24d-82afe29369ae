import logging
from pathlib import Path
from typing import List, Optional, Union
import os
import base64
import time
from io import BytesIO

try:
    import pymupdf
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False

try:
    from llama_index.core import Document as LlamaDocument
    LLAMA_INDEX_AVAILABLE = True
except ImportError:
    LLAMA_INDEX_AVAILABLE = False

logger = logging.getLogger(__name__)

class PDFReader:
    """
    A reader for PDF documents, capable of extracting text, images, and generating summaries
    using a multi-modal LLM (Gemini).
    """
    def __init__(self, api_key: Optional[str] = None):
        if not PYMUPDF_AVAILABLE:
            raise ImportError("PyMuPDF is not installed. Please install it with: pip install pymupdf")
        if not GENAI_AVAILABLE:
            raise ImportError("Google GenerativeAI is not installed. Please install it with: pip install google-generativeai")
        if not LLAMA_INDEX_AVAILABLE:
            raise ImportError("LlamaIndex is not installed. Please install it with: pip install llama-index")

        if not api_key:
            api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY is not provided. Please set it as an environment variable or pass it directly.")
        genai.configure(api_key=api_key)
        self.genai_model = genai.GenerativeModel("gemini-1.5-flash")

    def _encode_image(self, image_bytes: bytes) -> str:
        """Encodes image bytes to base64."""
        return base64.b64encode(image_bytes).decode('utf-8')

    def _extract_text_from_image_llm(self, image_bytes: bytes) -> str:
        """
        Extracts main text content from an image using Gemini LLM.
        Ignores text within diagrams, figures, charts, or tables.
        """
        try:
            base64_image = self._encode_image(image_bytes)
            contents = [
                {"mime_type": "image/png", "data": base64_image},
                {"text": "Extract the main text content from this page as it is without summarising or rephrasing it and maintain its order of occurence. Ignore text within diagrams, figures, charts, or tables - do not extract or list labels, component names, or any text that appears within visual elements. Only extract text that appears as paragraphs, notes, or warnings on the page itself."}
            ]
            response = self.genai_model.generate_content(contents)
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                raise Exception(f"Gemini blocked the prompt for text extraction: {response.prompt_feedback.block_reason}")
            return "".join([part.text for part in response.candidates[0].content.parts if hasattr(part, 'text')])
    
        except Exception as e:
            logger.warning(f"Error extracting text from image with LLM: {e}")
            return f"Error extracting text: {e}"
    time.sleep(15)
    def _describe_images_from_image_llm(self, image_bytes: bytes) -> str:
        """
        Identifies and describes diagrams, figures, or charts from an image using Gemini LLM.
        Excludes tables and focuses on concise summaries.
        """
        try:
            base64_image = self._encode_image(image_bytes)
            contents = [
                {"mime_type": "image/png", "data": base64_image},
                {"text": "Describe only the diagrams, figures, charts, or visual elements present in this image. Exclude any tables. Ensure the description is placed exactly under the text it explains and clearly indicates which part of the text it is related to (e.g., by referencing figure numbers or section titles mentioned in the text). If there is a figure number or caption, include that in your description. Do not extract or list individual text labels, component names, or any text that appears within these visuals. Instead, provide a concise summary of what each diagram represents and its purpose."}
            ]
            response = self.genai_model.generate_content(contents)
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                raise Exception(f"Gemini blocked the prompt for image description: {response.prompt_feedback.block_reason}")
            return "".join([part.text for part in response.candidates[0].content.parts if hasattr(part, 'text')])
        except Exception as e:
            logger.warning(f"Error describing images with LLM: {e}")
            return "No visual elements described in the image."

    def _summarize_tables_from_image_llm(self, image_bytes: bytes) -> str:
        """
        Identifies and summarizes tables from an image using Gemini LLM.
        Focuses on content, key data, and purpose, excluding individual cell values.
        """
        try:
            base64_image = self._encode_image(image_bytes)
            contents = [
                {"mime_type": "image/png", "data": base64_image},
                {"text": "Identify any tables present in this image. For each table, provide a concise summary of its content, key data, and purpose. If there is a table number or caption, include that in your summary. Do not extract or list individual cell values. Focus on the overall meaning and main takeaways from the table. If no tables are found, respond with 'No tables found in this image.'."}
            ]
            response = self.genai_model.generate_content(contents)
            if response.prompt_feedback and response.prompt_feedback.block_reason:
                raise Exception(f"Gemini blocked the prompt for table summarization: {response.prompt_feedback.block_reason}")
            return "".join([part.text for part in response.candidates[0].content.parts if hasattr(part, 'text')])
        except Exception as e:
            logger.warning(f"Error summarizing tables with LLM: {e}")
            return "No tables found in this image."

    def load_data(self, file_path: Union[str, Path]) -> List[LlamaDocument]:
        """
        Loads data from a PDF file, extracting text, image descriptions, and table summaries
        per page using LLM capabilities, and returns a list of Llama Documents.
        """
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"PDF file not found at {file_path}")

        doc = pymupdf.open(file_path)
        pdf_title = file_path.stem
        llama_documents: List[LlamaDocument] = []
        full_document_text = ""

        for page_num in range(doc.page_count):
            page = doc[page_num]
            
            # Generate a screenshot of the page in memory (no disk write for this intermediate step)
            matrix = pymupdf.Matrix(2, 2)
            pix = page.get_pixmap(matrix=matrix, alpha=False)
            img_bytes = pix.pil_tobytes(format="PNG") # Get image bytes directly

            # Extract text, image descriptions, and table summaries using LLM
            extracted_text = self._extract_text_from_image_llm(img_bytes)
            time.sleep(15)
            image_description = self._describe_images_from_image_llm(img_bytes)
            time.sleep(15)
            table_summary = self._summarize_tables_from_image_llm(img_bytes)
            time.sleep(15)

            # Combine all extracted content for the page
            page_content = extracted_text
            if image_description and "No visual elements described" not in image_description:
                page_content += f"\n\nImage Summary: {image_description}"
            if table_summary and "No tables found" not in table_summary:
                page_content += f"\n\nTable Summary: {table_summary}"

            # Append to full document text for an overall document representation
            full_document_text += f"\n\n--- Page {page_num + 1} ---\n{page_content}"


        # create a single LlamaDocument for the entire PDF
        full_pdf_doc = LlamaDocument(
            text=full_document_text,
            metadata={
                "file_name": file_path.name,
                "file_path": str(file_path),
                "pdf_title": pdf_title,
                "total_pages": doc.page_count,
                "extraction_method": "PDF Reader",
                "source_type": "PDF"
            },
            id_=f"{pdf_title}_full_document"
        )
        llama_documents.append(full_pdf_doc)

        return llama_documents

    def load_data_from_bytes(self, content: bytes, filename: str) -> List[LlamaDocument]:
        """
        Loads data from PDF bytes, extracting text, image descriptions, and table summaries
        per page using LLM capabilities, and returns a list of Llama Documents.
        """
        # Create a temporary BytesIO object
        pdf_stream = BytesIO(content)
        
        # Open the PDF from the stream
        doc = pymupdf.open(stream=pdf_stream, filetype="pdf")
        pdf_title = filename
        llama_documents: List[LlamaDocument] = []
        full_document_text = ""

        for page_num in range(doc.page_count):
            page = doc[page_num]
            
            # Generate a screenshot of the page in memory
            matrix = pymupdf.Matrix(2, 2)
            pix = page.get_pixmap(matrix=matrix, alpha=False)
            img_bytes = pix.pil_tobytes(format="PNG")

            # Extract text, image descriptions, and table summaries using LLM
            extracted_text = self._extract_text_from_image_llm(img_bytes)
            time.sleep(15)
            image_description = self._describe_images_from_image_llm(img_bytes)
            time.sleep(15)
            table_summary = self._summarize_tables_from_image_llm(img_bytes)
            time.sleep(15)

            # Combine all extracted content for the page
            page_content = extracted_text
            if image_description and "No visual elements described" not in image_description:
                page_content += f"\n\nImage Summary: {image_description}"
            if table_summary and "No tables found" not in table_summary:
                page_content += f"\n\nTable Summary: {table_summary}"

            # Append to full document text
            full_document_text += f"\n\n--- Page {page_num + 1} ---\n{page_content}"

        # Create a LlamaDocument for the entire PDF
        llama_doc = LlamaDocument(
            text=full_document_text,
            metadata={"title": pdf_title}
        )
        llama_documents.append(llama_doc)

        return llama_documents
