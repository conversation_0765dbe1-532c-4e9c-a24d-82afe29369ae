import logging
from collections import deque
from typing import Dict, List, Optional
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup
from llama_index.core import Document
from llama_index.readers.web import SimpleWebPageReader

logger = logging.getLogger(__name__)


def get_sitemap_urls(sitemap_url: str) -> List[str]:
    """
    Extract all URLs from a sitemap using iterative DFS to handle nested sitemaps.

    Args:
        sitemap_url (str): URL of the sitemap to parse

    Returns:
        List[str]: List of URLs found in the sitemap

    Raises:
        requests.RequestException: If there's an error fetching the sitemap
        ValueError: If the XML is invalid or couldn't be parsed
    """
    urls = []
    seen_sitemaps = set()  # Track processed sitemaps to avoid cycles
    stack = deque([sitemap_url])  # Use deque as a stack for DFS

    while stack:
        current_sitemap = stack.pop()

        # Skip if we've already processed this sitemap
        if current_sitemap in seen_sitemaps:
            continue

        seen_sitemaps.add(current_sitemap)

        try:
            headers = {
                "Accept": "*/*",
                "User-Agent": "HappyFox Cortex/1.0",
            }
            # Fetch the sitemap
            response = requests.get(current_sitemap, timeout=30, headers=headers)
            response.raise_for_status()

            # Parse XML content with BeautifulSoup
            soup = BeautifulSoup(response.content, "xml")

            # Process nested sitemaps (add to stack for DFS)
            for sitemap in soup.find_all("sitemap"):
                loc = sitemap.find("loc")
                if loc and loc.text:
                    nested_url = loc.text.strip()
                    if nested_url not in seen_sitemaps:
                        stack.append(nested_url)

            # Extract page URLs from current sitemap
            for url_element in soup.find_all("url"):
                loc = url_element.find("loc")
                if loc and loc.text:
                    urls.append(loc.text.strip())

        except requests.RequestException as e:
            logger.info("Error fetching sitemap %s: %s", current_sitemap, e)
            continue  # Skip this sitemap and continue with others
        except Exception as e:
            logger.info("Error parsing sitemap from %s: %s", current_sitemap, e)
            continue  # Skip this sitemap and continue with others

    # Remove duplicates while preserving order
    return list(dict.fromkeys(urls))


def get_all_site_urls(base_url: str) -> List[str]:
    """
    Try to find and parse the sitemap for a website starting from its base URL.

    Args:
        base_url (str): Base URL of the website (e.g., 'https://example.com')

    Returns:
        List[str]: List of all URLs found in the sitemap
    """
    # Common sitemap locations to try
    sitemap_paths = [
        "/sitemap.xml",
        "/sitemap_index.xml",
        "/sitemap/",
        "/sitemap/sitemap.xml",
        "/wp-sitemap.xml",  # WordPress default
    ]

    for path in sitemap_paths:
        try:
            sitemap_url = urljoin(base_url.rstrip("/") + "/", path.lstrip("/"))
            urls = get_sitemap_urls(sitemap_url)
            if urls:  # Only return if we found some URLs
                return urls
        except (requests.RequestException, ValueError):
            continue

    raise ValueError(f"Could not find a valid sitemap for {base_url}")


class WebpageReader(SimpleWebPageReader):
    def load_data(self, urls: List[str]) -> List[Document]:
        if not isinstance(urls, list):
            raise ValueError("urls must be a list of strings.")
        documents = []
        headers = {
            "Accept": "*/*",
            "User-Agent": "HappyFox Cortex/1.0",
        }
        for url in urls:
            response = requests.get(url, headers=headers).text
            soup = BeautifulSoup(response, "html.parser")
            metadata: Optional[Dict] = {
                "title": soup.title.text.strip() if soup.title else url,
                "url": url,
            }
            if self.html_to_text:
                import html2text

                response = html2text.html2text(response)
            if self._metadata_fn is not None:
                _metadata = self._metadata_fn(url)
                metadata.update(_metadata)

            documents.append(Document(text=response, id_=url, metadata=metadata or {}))

        return documents
