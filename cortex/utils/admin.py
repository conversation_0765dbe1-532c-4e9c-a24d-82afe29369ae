from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin.utils import lookup_field
from django.core.exceptions import FieldDoesNotExist
from django.db.models import J<PERSON><PERSON>ield
from django.http import FileResponse, HttpResponse, HttpResponseRedirect
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget


class AutoCompleteMixin:
    """
    Mixin that enables autocomplete functionality on related fields in the Django admin.
    It currently supports only ForeignKey and OneToOneField fields. The autocomplete
    functionality for fields listed in skip_autocomplete_fields will be skipped.
    AutoCompleteMixin must be included before ModelAdmin, otherwise it won't work.

    Attributes:
    RELATED_FIELD_TYPES: Tuple that holds the types of fields that support autocomplete.
    GENERIC_FOREIGNKEY_FIELDS: Tuple that holds the field names which are of type GenericForeignKey.
    Autocomplete is always skipped for these fields. skip_autocomplete_fields: Tuple that holds the field names for
    which autocomplete needs to be skipped. This can be overridden in the child class.
    related_fields: List that will hold the related fields (ForeignKey and OneToOneField) of the model, for which
    autocomplete needs to be enabled.
    """

    RELATED_FIELD_TYPES = ("ForeignKey", "OneToOneField")

    # Generic ForeignKey Fields always skip for autocomplete, since auto-completing them causes errors.
    GENERIC_FOREIGNKEY_FIELDS = ("content_object", "content_type", "object_id")
    skip_autocomplete_fields = ()

    def __init__(self, model, admin_site):
        """
        Initialize the mixin and set the related fields for autocomplete.

        Args:
            model: Model object for which autocomplete should be set up.
            admin_site: Current admin site object.
        """
        super().__init__(model, admin_site)
        fields = [field for field in self.model._meta.get_fields() if field.name not in self.GENERIC_FOREIGNKEY_FIELDS]
        self.related_fields = [field.name for field in fields if field.get_internal_type() in self.RELATED_FIELD_TYPES]
        for field in self.skip_autocomplete_fields:
            if field in self.autocomplete_fields:
                raise ValueError(
                    _("autocomplete_fields and skip_autocomplete_fields cannot contain same field: %s") % field
                )

    def get_autocomplete_fields(self, _):
        """
        Return the fields for which autocomplete is enabled.

        Args:
            _: Current HTTP request.

        Returns:
            A set of fields for which autocomplete is enabled.
        """
        autocomplete_fields = set(self.related_fields)
        autocomplete_fields.update(self.autocomplete_fields)
        if self.skip_autocomplete_fields:
            return autocomplete_fields - set(self.skip_autocomplete_fields)
        return autocomplete_fields


class CustomModelAdmin(AutoCompleteMixin, admin.ModelAdmin):
    """
    A CustomModelAdmin that extends from the AutoCompleteMixin and Django's ModelAdmin.
    It ensures all the models inherit the global behaviors defined in this class.
    The deleted records can be restored via Django's actions dropdown.

    Attributes:
    readonly_fields: Tuple that holds the fields which are read-only in the Django admin.
    actions: List that holds the custom actions defined for the model in the Django admin.
    """

    formfield_overrides = {
        JSONField: {"widget": JSONEditorWidget},
    }

    base_readonly_fields = (
        "uuid",
        "created_at",
        "updated_at",
        "deleted_at",
        "created_by",
        "updated_by",
        "deleted_by",
    )

    def __init__(self, model, admin_site):
        super().__init__(model, admin_site)
        # Check for the presence of created_by, updated_by, and deleted_by in model fields
        readonly_fields = []
        for field in self.base_readonly_fields:
            try:
                model._meta.get_field(field)
                readonly_fields.append(field)
            except FieldDoesNotExist:
                continue
        # Set readonly_fields based on the presence of these fields in the model
        self.readonly_fields = readonly_fields

    def has_delete_permission(self, request, obj=None):
        """We want to disable delete action by default. Use DeleteActionMixin to change this behaviour"""
        return not settings.IS_PROD

    def save_model(self, request, obj, form, change):
        """
        Override the save model function to keep track of the user who creates and updates the object.

        Args:
            request: Current HTTP request.
            obj: The model object to be saved.
            form: Form instance with the data from the request.
            change: Boolean value representing whether this is a new object or an existing one.
        """
        if not change and hasattr(obj, "created_by"):
            obj.created_by_id = request.user.pk
        if hasattr(obj, "updated_by"):
            obj.updated_by_id = request.user.pk
        if hasattr(obj, "deleted_by") and obj.deleted_by:
            obj.is_active = False

        super().save_model(request, obj, form, change)


class DeleteActionMixin:
    """A mixin to enable the delete action on the list page and the delete button on the detail page of admin."""

    @staticmethod
    def has_delete_permission(_request, _obj=None):
        """
        Checks if delete permissions are given.

        Args:
            _request: Current HTTP request.
            _obj: The model object for which the permission is checked.

        Returns:
            True if permission is granted else False. Here it always returns True.
        """
        return True


class ReadOnlyMixin:
    """
    This mixin disables all operations (create, update, and delete) on the model admin.

    Methods:
    has_add_permission: Returns False to disable adding (creating) new instances of the model.
    has_change_permission: Returns False to disable changing (updating) existing instances of the model.
    has_delete_permission: Returns False to disable deleting instances of the model.
    """

    @staticmethod
    def has_add_permission(_request):
        """
        Deny adding (creating) new instances of the model.

        Args:
            _request: Current HTTP request.

        Returns:
            False, denying the permission to add.
        """
        return False

    @staticmethod
    def has_change_permission(_request, _obj=None):
        """
        Deny changing (updating) existing instances of the model.

        Args:
            _request: Current HTTP request.
            _obj: The model object for which the permission is checked.

        Returns:
            False, denying the permission to change.
        """
        return False

    @staticmethod
    def has_delete_permission(_request, _obj=None):
        """
        Deny deleting instances of the model.

        Args:
            _request: Current HTTP request.
            _obj: The model object for which the permission is checked.

        Returns:
            False, denying the permission to delete.
        """
        return False


class DeletedFilter(admin.SimpleListFilter):
    """
    Custom filter to check if deleted_at is None or not
    """

    title = _("Is Deleted")
    parameter_name = "is_deleted"

    def lookups(self, request, model_admin):
        return (
            ("true", _("Yes")),
            ("false", _("No")),
        )

    def queryset(self, request, queryset):
        if self.value() == "true":
            return queryset.filter(deleted_at__isnull=False)
        if self.value() == "false":
            return queryset.filter(deleted_at__isnull=True)

        return queryset
