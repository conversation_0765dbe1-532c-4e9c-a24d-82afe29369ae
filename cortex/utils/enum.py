from django.db import models
from django.utils.translation import gettext_lazy as _


class DataSourceType(models.TextChoices):
    WEB_CRAWLER = "web_crawler", _("Web Crawler")
    NOTION = "notion", _("Notion")
    GDRIVE = "gdrive", _("Google Drive")
    ONEDRIVE = "onedrive", _("OneDrive")
    UPLOAD = "upload", _("Direct Upload")
    TEXT = "text", _("Text")
    CUSTOM = "custom", _("Custom")
    PDF = "pdf", _("PDF")


class DataSourceStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    PROCESSING = "processing", _("Processing")
    COMPLETED = "completed", _("Completed")
    FAILED = "failed", _("Failed")
    SYNCING = "syncing", _("Syncing")


class DocumentStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    PROCESSING = "processing", _("Processing")
    PROCESSED = "processed", _("Processed")
    FAILED = "failed", _("Failed")


class KnowledgeBaseType(models.TextChoices):
    VECTOR_STORE = "vector_store", _("Vector Store")
    STRUCTURED_DATA = "structured_data", _("Structured Data")
    GENAI_INDEX = "genai_index", _("GenAI Index")


class KnowledgeBaseStatus(models.TextChoices):
    CREATING = "creating", _("Creating")
    ACTIVE = "active", _("Active")
    UPDATING = "updating", _("Updating")
    ERROR = "error", _("Error")


class EmbeddingModel(models.TextChoices):
    DEFAULT = "default", _("Default")
    COHERE_MULTILINGUAL_EMBEDDING = "cohere_multilingual_embedding", _("Cohere Multilingual Embedding")


class ChunkingStrategy(models.TextChoices):
    DEFAULT = "default", _("Default Chunking")
    FIXED_SIZE = "fixed_size", _("Fixed-size Chunking")
    HIERARCHICAL = "hierarchical", _("Hierarchical Chunking")
    SEMANTIC = "semantic", _("Semantic Chunking")
    NONE = "none", _("No Chunking")


class ToolType(models.TextChoices):
    SEARCH = "search", _("Search")
    BROWSE = "browse", _("Browse")


class IngestionJobType(models.TextChoices):
    FULL = "FULL", _("Full")
    INCREMENTAL = "INCREMENTAL", _("Incremental")
    REINDEX = "REINDEX", _("Reindex")


class IngestionJobStatus(models.TextChoices):
    PENDING = "pending", _("Pending")
    PROCESSING = "processing", _("Processing")
    COMPLETED = "completed", _("Completed")
    FAILED = "failed", _("Failed")


class AuthType(models.TextChoices):
    NONE = "NONE", _("None")
    BASIC = "BASIC", _("Basic")
    API_KEY = "API_KEY", _("API Key")
    OAUTH2 = "OAUTH2", _("OAuth 2.0")


class ToolCallStatus(models.TextChoices):
    PENDING = "PENDING", _("Pending")
    SUCCESS = "SUCCESS", _("Success")
    ERROR = "ERROR", _("Error")


class MCPCallStatus(models.TextChoices):
    PENDING = "PENDING", _("Pending")
    SUCCESS = "SUCCESS", _("Success")
    ERROR = "ERROR", _("Error")


class AgentTaskStatus(models.TextChoices):
    PENDING = "PENDING", _("Pending")
    RUNNING = "RUNNING", _("Running")
    COMPLETED = "COMPLETED", _("Completed")
    FAILED = "FAILED", _("Failed")
    CANCELLED = "CANCELLED", _("Cancelled")
