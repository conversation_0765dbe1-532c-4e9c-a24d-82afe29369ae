import logging
from typing import Dict, List

from bs4 import BeautifulSoup
from html2text import HTML2Text
from llama_index.core.schema import Document, TransformComponent

from accounts.models import Account
from services import get_claude_llm

logger = logging.getLogger(__name__)

ARTICLE_TOPIC_PROMPT = """
You are an AI Assistant working for {company_name} who works in the {domain} domain.

Here is some background information about the company:
<company_info>
{company_info}
</company_info>

You are an expert at understanding knowledge base articles and providing relevant information to the users.

Your task is to assign the most suitable topic(s) to the given article from the list of given topics.
Here are the topics that you can assign:
<topics>
    {topics}
</topics>

If the article does not match any of the topics, you can leave the fields empty.
Here is how you should respond:
<response>
    <topics_assigned>
        <topic_assigned>
            <name>Name of the topic assigned to the article</name>
            <id>ID of the topic assigned to the article</id>
        </topic_assigned>
        <topic_assigned>
            <name>Name of the topic assigned to the article</name>
            <id>ID of the topic assigned to the article</id>
        </topic_assigned>
    </topics_assigned>
</response>

Here is the KB article that you need to analyze:
<article>
    {article}
</article>

You must respond in the format mentioned above and nothing else.
While assigning topics, make sure to assign the most relevant ones.
If the article is not related to any of the given topics, you can leave the fields empty.
"""

# This will be used for both webpages and documents
CONTENT_TOPIC_PROMPT = """
You are an AI Assistant working for {company_name} who works in the {domain} domain.

Here is some background information about the company:
<company_info>
{company_info}
</company_info>

You are an expert at understanding the given knowledge and providing relevant information to the users.

Your task is to assign the most suitable topic(s) to the given knowledge/content from the list of given topics.
Here are the topics that you can assign:
<topics>
    {topics}
</topics>

If the given knowledge/content does not match any of the topics, you can leave the fields empty.
Here is how you should respond:
<response>
    <topics_assigned>
        <topic_assigned>
            <name>Name of the topic assigned to the knowledge/content</name>
            <id>ID of the topic assigned to the knowledge/content</id>
        </topic_assigned>
        <topic_assigned>
            <name>Name of the topic assigned to the knowledge/content</name>
            <id>ID of the topic assigned to the knowledge/content</id>
        </topic_assigned>
    </topics_assigned>
</response>

Here is the knowledge/content that you need to analyze:
<knowledge>
    {knowledge}
</knowledge>

You must respond in the format mentioned above and nothing else.
While assigning topics, make sure to assign the most relevant ones.
If the knowledge/content is not related to any of the given topics or products, you can leave the fields empty.
When you decide to assign a topic, make sure you always give the ID of the topic along with the name. Do not leave the ID field empty.
"""


class BaseTopicTaggingComponent(TransformComponent):
    """Base class for topic tagging components to avoid code duplication."""

    account: Account
    topics: list

    def __init__(self, account: Account, **kwargs):
        """Initialize the component with an account and its topics."""
        super().__init__(account=account, topics=account.topics_set.filter(is_active=True))

    def _get_topics_context(self) -> str:
        """Generate XML context string for all available topics."""
        topic_context_str = ""
        for topic in self.topics:
            topic_context_str += f"""
            <topic>
                <topic_id> {topic.id} </topic_id>
                <topic_name> {topic.name} </topic_name>
                <description> {topic.description} </description>
            </topic>
            """
        return topic_context_str

    def _get_node_context(self, node: Document) -> str:
        """Generate context for the node - to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _get_node_context")

    def _get_prompt_template(self) -> str:
        """Return the prompt template - to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _get_prompt_template")

    def _get_topic(self, node: Document) -> List[int]:
        """Determine topic IDs for a document using Claude LLM."""
        topic_context_str = self._get_topics_context()
        node_context_str = self._get_node_context(node)
        company_info = self.account.settings["company_info"]
        company_name = self.account.settings["company_name"]
        domain = self.account.settings["domain"]

        # Build prompt with the appropriate template and context
        prompt = self._get_prompt_template().format(
            topics=topic_context_str,
            company_info=company_info,
            company_name=company_name,
            domain=domain,
            **self._get_prompt_params(node_context_str),
        )
        # Get response from Claude
        claude_llm = get_claude_llm()
        response = claude_llm.complete(prompt)

        # Parse response to extract topic IDs
        soup = BeautifulSoup(response.text, "html.parser")
        topics_assigned = soup.find_all("topic_assigned")
        logger.info("Topics assigned: %s", topics_assigned)
        topic_ids_assigned = [int(topic.find("id").text) for topic in topics_assigned]

        return topic_ids_assigned

    def _get_prompt_params(self, node_context: str) -> Dict[str, str]:
        """Get parameters specific to the prompt template - to be implemented by subclasses."""
        raise NotImplementedError("Subclasses must implement _get_prompt_params")

    def __call__(self, nodes, **kwargs):
        """Process all nodes and tag them with topics."""
        logger.info("Tagging %s nodes with topics", len(nodes))
        for node in nodes:
            topic_ids = self._get_topic(node)
            node.metadata[f"{self.account.uuid}__topic_ids"] = topic_ids

        return nodes


class TagTopicsForContentTransformComponent(BaseTopicTaggingComponent):
    """Component for tagging general content with topics."""

    def _get_node_context(self, node: Document) -> str:
        """Generate content-specific context from a node."""
        context_str = f"""
            <content> {node.text} </content>
        """
        return context_str

    def _get_prompt_template(self) -> str:
        """Return the content-specific prompt template."""
        return CONTENT_TOPIC_PROMPT

    def _get_prompt_params(self, node_context: str) -> Dict[str, str]:
        """Get content-specific prompt parameters."""
        return {"knowledge": node_context}


class ConvertHTMLToMD(TransformComponent):
    """Component for converting HTML content to Markdown format."""

    def __get_md(self, node: Document) -> str:
        """
        Convert HTML text to markdown text.

        Args:
            node: Document with HTML content

        Returns:
            str: Markdown formatted text
        """
        handler = HTML2Text()
        # Refer https://github.com/Alir3z4/html2text/blob/master/docs/usage.md
        # for configurations used below.
        handler.body_width = 0  # To avoid word wrapping
        handler.inline_links = True
        handler.use_automatic_links = False
        return handler.handle(node.text)

    def __call__(self, nodes, **kwargs):
        """Process all nodes and convert HTML to Markdown."""
        for node in nodes:
            node.set_content(self.__get_md(node))
        return nodes
