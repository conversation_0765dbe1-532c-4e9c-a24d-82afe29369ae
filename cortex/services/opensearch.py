import boto3
from django.conf import settings
from llama_index.core import VectorStoreIndex
from llama_index.vector_stores.opensearch import OpensearchVectorClient, OpensearchVectorStore
from opensearchpy import (
    AsyncHttpConnection,
    AsyncOpenSearch,
    AWSV4Signer<PERSON>ync<PERSON>uth,
    AWSV4SignerAuth,
    OpenSearch,
    RequestsHttpConnection,
)
from opensearchpy.helpers import bulk
from llama_index.core.vector_stores.types import MetadataFilter, FilterOperator

from services import get_cohere_embedding_model
from utils.constants import COHERE_EMBEDDING_MODEL_OUTPUT_DIM, OPENSEARCH_INDEX_NAME

DEFAULT_BULK_OPS_SETTINGS = {
    # No of times to retry after initial failure
    "max_retries": 3,
    # No of secs to wait before initial retry. Used for calculating wait times for subsequest requests as well.
    "initial_backoff": 10,
    # Max time out.
    "max_backoff": 30,
    # number of docs in one chunk sent to es
    "chunk_size": 20,
    # max size of one chunk sent to es
    "max_chunk_bytes": 10 * 1024 * 1024,
}


class CustomOpensearchVectorClient(OpensearchVectorClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _parse_filter(self, filter: MetadataFilter) -> dict:
        key = f"metadata.{filter.key}"
        op = filter.operator

        if op == FilterOperator.IS_EMPTY:
            return {"bool": {"must_not": {"exists": {"field": key}}}}

        return super()._parse_filter(filter)


def get_opensearch_vector_store() -> OpensearchVectorStore:
    opensearch_client = OpensearchClient().get_client()
    opensearch_async_client = AsyncOpensearchClient().get_client()
    opensearch_endpoint = OpensearchClient().get_endpoint()

    opensearch_vector_client = CustomOpensearchVectorClient(
        os_client=opensearch_client,
        os_async_client=opensearch_async_client,
        embedding_field="embedding",
        text_field="content",
        dim=COHERE_EMBEDDING_MODEL_OUTPUT_DIM,
        index=OPENSEARCH_INDEX_NAME,
        endpoint=opensearch_endpoint,
    )
    vector_store = OpensearchVectorStore(opensearch_vector_client)
    return vector_store


class AsyncOpensearchClient:
    def __init__(self):
        self.client = self.__get_opensearch_client()

    def __get_opensearch_client(self):
        host = settings.OPENSEARCH_HOST
        port = settings.OPENSEARCH_PORT

        if settings.OPENSEARCH_AUTH == "iam":
            region = settings.OPENSEARCH_REGION

            credentials = boto3.Session().get_credentials()
            auth = AWSV4SignerAsyncAuth(credentials, region)

            client = AsyncOpenSearch(
                hosts=[f"{host}:{port}"],
                http_auth=auth,
                use_ssl=True,
                verify_certs=True,
                connection_class=AsyncHttpConnection,
            )
        else:
            client = AsyncOpenSearch(hosts=[{"host": host, "port": int(port)}])

        return client

    def get_client(self):
        return self.client

    async def search(self, index_name, query):
        response = await self.client.search(index=index_name, body=query)
        return response

    async def close(self):
        await self.client.close()


class OpensearchClient:
    def __init__(self):
        self.client = self.__get_opensearch_client()

    def __get_opensearch_client(self):
        host = settings.OPENSEARCH_HOST
        port = settings.OPENSEARCH_PORT

        if settings.OPENSEARCH_AUTH == "iam":
            region = settings.OPENSEARCH_REGION

            credentials = boto3.Session().get_credentials()
            auth = AWSV4SignerAuth(credentials, region)

            client = OpenSearch(
                hosts=[f"{host}:{port}"],
                http_auth=auth,
                use_ssl=True,
                verify_certs=True,
                connection_class=RequestsHttpConnection,
            )
        else:
            client = OpenSearch(hosts=[{"host": host, "port": port}])

        return client

    def create_index_template(self, name, template):
        response = self.client.indices.put_template(name, template)
        return response

    def create_index(self, index_name):
        response = self.client.indices.create(index_name)
        return response

    def delete_index(self, index_name):
        response = self.client.indices.delete(index_name)
        return response

    def close_index(self, index_name):
        response = self.client.indices.close(index=index_name)
        return response

    def open_index(self, index_name):
        response = self.client.indices.open(index_name)
        return response

    def does_index_exist(self, index_name):
        response = self.client.indices.exists(index_name)
        return response

    def create_alias(self, index_name, alias_name):
        response = self.client.indices.put_alias(index=index_name, name=alias_name)
        return response

    def delete_alias(self, index_name, alias_name):
        response = self.client.indices.delete_alias(index=index_name, name=alias_name)
        return response

    def get_alias(self, index_name, alias_name):
        response = self.client.indices.get_alias(index=index_name, name=alias_name)
        return response

    def exists_alias(self, index_name, alias_name):
        response = self.client.indices.exists_alias(index=index_name, name=alias_name)
        return response

    def update_index_for_alias(self, alias_name, old_index_name, new_index_name):
        response = self.client.indices.update_aliases(
            body={
                "actions": [
                    {"remove": {"index": old_index_name, "alias": alias_name}},
                    {"add": {"index": new_index_name, "alias": alias_name}},
                ]
            }
        )
        return response

    def index_document(self, index_name, doc_id, doc):
        response = self.client.index(index=index_name, id=doc_id, body=doc)
        return response

    def delete_document(self, index_name, doc_id):
        response = self.client.delete(index=index_name, id=doc_id)
        return response

    def search(self, index_name, query):
        response = self.client.search(index=index_name, body=query)
        return response

    def bulk_index(self, index_name, docs):
        """
        Bulk indexes multiple docs in an index.

        Parameters:

        index_name: Name of the index
        docs: a dictionary of documents with
        document id as key , and document content to
        be indexed as value.
        """
        actions = []
        for doc_id in docs.keys():
            index_action = {
                "_op_type": "index",
                "_index": index_name,
                "_id": doc_id,
            }
            index_action.update(docs[doc_id])
            actions.append(index_action)

        response = bulk(self.client, actions, **DEFAULT_BULK_OPS_SETTINGS)
        return response

    def bulk_delete(self, index_name: str, doc_ids: list):
        """
        Bulk deletes docs in a given index.
        """
        actions = []
        for doc_id in doc_ids:
            delete_action = {"_op_type": "delete", "_index": index_name, "_id": doc_id}
            actions.append(delete_action)

        response = bulk(self.client, actions, raise_on_error=False, **DEFAULT_BULK_OPS_SETTINGS)
        return response

    def get_document_ids_by_query(self, index_name, key, value):
        """
        Returns a list of document ids that match the given query.
        """
        doc_ids = []
        query = {"query": {"match": {key: value}}, "_source": ["_id"]}
        data = self.client.search(index=index_name, body=query, scroll="1m", size=1000)
        scroll_id = data["_scroll_id"]
        while len(data["hits"]["hits"]):
            doc_ids.extend([hit["_id"] for hit in data["hits"]["hits"]])
            data = self.client.scroll(scroll_id=scroll_id, scroll="1m")
            scroll_id = data["_scroll_id"]
        self.client.clear_scroll(scroll_id=scroll_id)
        return doc_ids

    def get_client(self):
        return self.client

    def get_endpoint(self):
        protocol = "http" if settings.DEBUG else "https"
        return f"{protocol}://{settings.OPENSEARCH_HOST}:{settings.OPENSEARCH_PORT}"


def get_opensearch_vector_store_index() -> VectorStoreIndex:
    """
    Create and return an instance of VectorStoreIndex using the
    - Opensearch vector store
    - Cohere embedding model
    - Default storage context
    """
    vector_store = get_opensearch_vector_store()
    embed_model = get_cohere_embedding_model()
    vectore_store_index = VectorStoreIndex.from_vector_store(vector_store=vector_store, embed_model=embed_model)

    return vectore_store_index
