from django.conf import settings
from llama_index.embeddings.bedrock import BedrockEmbedding
from llama_index.postprocessor.bedrock_rerank import AWSBedrockRerank

from utils.constants import COHERE_EMBEDDING_MODEL_ID, COHERE_RERANK_M<PERSON>EL_ID


def get_cohere_embedding_model() -> BedrockEmbedding:
    return BedrockEmbedding(
        aws_access_key_id=settings.BEDROCK_ACCESS_KEY_ID,
        aws_secret_access_key=settings.BEDROCK_SECRET_ACCESS_KEY,
        region_name=settings.BEDROCK_REGION,
        model_name=COHERE_EMBEDDING_MODEL_ID,
    )


def get_cohere_rerank_model(top_n: int = 3) -> AWSBedrockRerank:
    return AWSBedrockRerank(
        aws_access_key_id=settings.BEDROCK_ACCESS_KEY_ID,
        aws_secret_access_key=settings.BEDROCK_SECRET_ACCESS_KEY,
        region_name=settings.BEDROCK_REGION,
        model_id=COHERE_RERANK_MODEL_ID,
        top_n=top_n,
    )
