from django.conf import settings
from llama_index.storage.docstore.postgres import PostgresDocumentStore

from utils.constants import POSTGRES_DOCSTORE_TABLE_NAME


def get_postgres_docstore(account_id, data_source_id) -> PostgresDocumentStore:
    """
    Returns a PostgresDocumentStore instance for the given account and data source.
    """
    namespace = f"account-{account_id}-data-source-{data_source_id}-docstore"
    return PostgresDocumentStore.from_params(
        host=settings.DATABASE_HOST,
        port=settings.DATABASE_PORT,
        user=settings.DATABASE_USER,
        password=settings.DATABASE_PASSWORD,
        database=settings.DATABASE_NAME,
        perform_setup=True,
        table_name=POSTGRES_DOCSTORE_TABLE_NAME,
        namespace=namespace,
    )
