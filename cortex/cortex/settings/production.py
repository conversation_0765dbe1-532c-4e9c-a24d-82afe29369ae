# pylint: disable=unused-wildcard-import,wildcard-import
import sentry_sdk
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.scrubber import DEFAULT_DENYLIST, EventScrubber

from cortex.settings.base import *  # noqa: F403

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Sentry is an open-source error tracking system that helps to monitor and fix crashes in real-time.
# Learn more about Sentry here: https://sentry.io/welcome/
SENTRY_REDACT_MODE = bool(int(os.getenv("SENTRY_REDACT_MODE", "1")))
denylist = DEFAULT_DENYLIST + [
    "ai_app_auth_token",
    "jwt_sso_shared_secret",
    "jwt_sso_token_issuer",
    "bedrock_access_key_id",
    "bedrock_secret_access_key",
    "api_base",
    "api_key",
    "user_email",
    "user_name",
    "user_profile_image",
]
sentry_sdk.init(
    dsn=ENV.str("SENTRY_DSN", default=None),  # Data Source Name for connecting Sentry
    integrations=[  # Integrations specify which parts of the system should be monitored
        DjangoIntegration(),
        CeleryIntegration(),
        RedisIntegration(),
    ],
    traces_sample_rate=0,
    # Prevent sensitive data from being sent to Sentry
    send_default_pii=False,
    event_scrubber=EventScrubber(denylist=denylist),
    max_breadcrumbs=0 if SENTRY_REDACT_MODE else 100,
    max_request_body_size="never" if SENTRY_REDACT_MODE else "always",
    include_local_variables=not SENTRY_REDACT_MODE,
    # Name of the environment (used in Sentry dashboard to differentiate between environments)
    environment=ENVIRONMENT,
)

# This disables the "browsable" api that can cause various production issues
REST_FRAMEWORK["DEFAULT_RENDERER_CLASSES"] = ("rest_framework.renderers.JSONRenderer",)
