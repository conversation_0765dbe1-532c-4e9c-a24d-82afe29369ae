import logging

import redis
from django.conf import settings
from django.db import connections
from django.http import HttpResponse, HttpResponseServerError
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)


class HealthCheckMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.method == "GET":
            if request.path == "/liveliness/":
                return self.liveness(request)
            elif request.path == "/readiness/":
                return self.readiness(request)
        return self.get_response(request)

    @staticmethod
    def liveness(request):
        """
        Returns that the server is alive.
        """
        _ = request  # Suppress unused parameter warning
        return HttpResponse("OK")

    @staticmethod
    def readiness(request):
        """
        Connect to each database and do a generic standard SQL query
        that doesn't write any data and doesn't depend on any tables
        being present.
        """
        _ = request  # Suppress unused parameter warning
        try:
            for name in connections:
                cursor = connections[name].cursor()
                cursor.execute("SELECT 1;")
                row = cursor.fetchone()
                if row is None:
                    return HttpResponseServerError(_("Database: Returned invalid response."))
        except Exception as e:  # pylint: disable=broad-except
            logger.exception(e)
            return HttpResponseServerError(_("Database: Cannot connect to the database."))

        try:
            r = redis.StrictRedis.from_url(settings.REDIS_URL)
            r.ping()
        except Exception as e:  # pylint: disable=broad-except
            logger.exception(e)
            return HttpResponseServerError(_("Redis: Cannot connect to the database."))

        return HttpResponse("OK")
