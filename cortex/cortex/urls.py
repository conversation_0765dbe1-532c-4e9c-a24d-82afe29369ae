"""
URL configuration for cortex project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.contrib import admin
from django.urls import include, path
import logging

logger = logging.getLogger(__name__)

urlpatterns = [
    # Include admin urls
    path(settings.ADMIN_ENDPOINT, admin.site.urls),
]

urlpatterns += [
    # Include api urls
    path("api/", include("cortex.routers.api")),
    
]

logger.info("URL patterns: %s", urlpatterns)
