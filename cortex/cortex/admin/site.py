from django.contrib import admin
from django.contrib.admin.forms import AdminAuthenticationForm
from django.forms import forms
from django.utils.translation import gettext_lazy as _


class SuperUserAuthenticationForm(AdminAuthenticationForm):
    def confirm_login_allowed(self, user):
        """
        Locks out anyone but superusers.
        """
        super().confirm_login_allowed(user)

        if not user.is_superuser:
            raise forms.ValidationError(
                _(
                    "Please enter the correct username and password for a superuser "
                    "account. Note that both fields may be case-sensitive."
                ),
                code="invalid_login",
            )


class AdminSite(admin.AdminSite):
    # Customize Django Admin
    site_header = "Cortex Admin"
    site_title = "Cortex Admin"
    index_title = "Welcome to Cortex Admin"
    login_form = SuperUserAuthenticationForm

    def register(self, model_or_iterable, admin_class=None, **options):
        # pylint: disable=import-outside-toplevel
        from utils.admin import CustomModelAdmin

        admin_class = admin_class or CustomModelAdmin
        super().register(model_or_iterable, admin_class=admin_class, **options)
