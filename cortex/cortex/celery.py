import logging
import os
from logging.config import dictConfig

from celery import Celery
from celery.signals import setup_logging, worker_shutdown, worker_shutting_down
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "cortex.settings.local")

app = Celery("cortex")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object(settings, namespace="CELERY")

# Set up logging
logger = logging.getLogger(__name__)


@setup_logging.connect
def config_loggers(*args, **kwargs):
    dictConfig(settings.LOGGING)


@worker_shutting_down.connect
def worker_shutting_down_handler(sig, how, exitcode, **kwargs):
    logger.info("Celery worker shutting down", extra={"signal": sig, "how": how, "exitcode": exitcode})


@worker_shutdown.connect
def worker_shutdown_handler(*args, **kwargs):
    logger.info("Celery worker shutdown")


# Load task modules from all registered Django apps.
app.autodiscover_tasks()
