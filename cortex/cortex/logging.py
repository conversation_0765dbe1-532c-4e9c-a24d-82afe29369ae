import copy
import os

import structlog

pre_chain = [
    # Add the log level
    structlog.stdlib.add_log_level,
    # Add the logger name
    structlog.stdlib.add_logger_name,
    # Add extra attributes of LogRecord objects to the event dictionary
    # so that values passed in the extra parameter of log methods pass
    # through to log output.
    structlog.stdlib.ExtraAdder(),
    # Add the timestamp
    structlog.processors.TimeStamper(fmt="iso", utc=True),
    structlog.processors.StackInfoRenderer(),
    # Format exception info in log records
    structlog.processors.format_exc_info,
]


BASE_LOGGING_CONFIG: dict = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processors": [
                structlog.stdlib.ProcessorFormatter.remove_processors_meta,
                structlog.processors.JSONRenderer(),
            ],
            "foreign_pre_chain": pre_chain,
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
    },
    "loggers": {
        # root logger
        "": {
            "level": os.getenv("DJANGO_LOG_LEVEL", "INFO"),
            "handlers": ["console"],
        },
    },
}

# Logging config specific to dev

# Remove exception formatter so that exceptions are better printed in console
dev_pre_chain = copy.copy(pre_chain)
dev_pre_chain.remove(structlog.processors.format_exc_info)

# Change log statements to be more readable in console
DEV_LOGGING_CONFIG: dict = copy.deepcopy(BASE_LOGGING_CONFIG)
DEV_LOGGING_CONFIG["formatters"]["console"] = {
    "()": structlog.stdlib.ProcessorFormatter,
    "processors": [
        structlog.stdlib.ProcessorFormatter.remove_processors_meta,
        structlog.dev.ConsoleRenderer(colors=True),
    ],
    "foreign_pre_chain": dev_pre_chain,
}
DEV_LOGGING_CONFIG["handlers"]["console"]["formatter"] = "console"
