{"docstore/metadata": {"d387c35d-8dc4-43ee-9bbe-c79a3ea4cf08": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "f3e3afca-b01c-41cb-aaf8-31f47ac39604": {"doc_hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc"}, "cdd913aa-4e50-41b0-b379-3c83cc79a8f7": {"doc_hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997"}, "0c07224e-6ec1-465b-9b80-c09dea2feabe": {"doc_hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921"}, "3e2b0089-22d8-4238-9435-28ec5bc9208e": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "58c5eea1-2caa-47f5-9672-4b803aabd16b": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "f76068c7-d66a-4d11-bc3b-32e3912ebddd": {"doc_hash": "7e90223e411255dbd5f3c8eb5645ec9e73b8f9d1d693366a4e9acc1382562af2"}, "39e759f9-b636-4bdd-ab7c-815792a8bb22": {"doc_hash": "399a0326ef42452699b755d565db2579b884ec8f6db759abc140741531f61003"}, "2aa38dd9-c60e-47e4-af9f-657958eb5c9e": {"doc_hash": "b2a31f86924844dff488930fb5558fd43a77f97ed341ef36bafdf82efae38d36"}, "44dfbad2-9b28-4b93-9ae0-df0ac22aedd1": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "7b2bfec4-6cbe-4613-8837-380ed3ddc797": {"doc_hash": "1496e48148b147e18519f2a7029b44051bfcf510422e148b1705c913d11ce852"}, "c9203a68-cce0-46c1-a2c1-51e84a7bb1a2": {"doc_hash": "56fdc6f005521788f7b25d646a50785c20f425413e05377c714665776ab8b066"}, "7b9820a0-ffec-4329-a538-c48faa3da47d": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "5624308a-dd90-4869-9795-d3271821f5b4": {"doc_hash": "c621b9db2852ee24de749a386a579f8cd218deb31c5b4676d904686cfc871309"}, "04f73f6f-58aa-4a97-b36d-1a1a9908cb5c": {"doc_hash": "bdf6b271c547142ecb31404b67156e3e30918f1aba4d14211e83ba0855fd3200"}, "6bda8a9f-4495-4259-a80a-d309d17b54c5": {"doc_hash": "b5e432fec16ec49695de556d9e4b9c99f23a9c24cd4712aa446ba9d980d0c270"}, "2cae6662-3d5c-4481-82d8-d213e869b0fe": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "e81807cc-d80b-4283-b8c5-d95301e5766c": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "922942b3-c87c-49f4-a621-fe86cea29b15": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "facc5d91-b971-46f8-b8d8-b7f116e56e6d": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "f22e9609-5bbc-444d-b270-0bf50f3edd33": {"doc_hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2"}, "8a74d797-f340-472c-90b2-2e5901bb2d4a": {"doc_hash": "3def1c6e187688949579135fb1ffb766200356e66a7a6e2ae4ed92810a293916"}, "55829c13-f8ab-45a6-bad1-92162ee79b05": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "f67c2943-9099-45ae-b257-4a328a76192b": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "d387c35d-8dc4-43ee-9bbe-c79a3ea4cf08"}, "2f6cadcb-19bf-453a-9d37-fdc3f7f36e6e": {"doc_hash": "e507945e2b39e3d6090cdfaad4a09752efad2de4e59be21ace9ef2e9a7aa4eec", "ref_doc_id": "f3e3afca-b01c-41cb-aaf8-31f47ac39604"}, "033ac525-4ddc-4324-af88-7cb68162cfe2": {"doc_hash": "e8076cdbf063a169a85606ab841d0b0dbe7f0ae8d5923f8d17cbadc695115ebc", "ref_doc_id": "cdd913aa-4e50-41b0-b379-3c83cc79a8f7"}, "121cfa84-2eeb-4775-8029-1a3b35e9cbb6": {"doc_hash": "d5252924f1f1e32df134e8dbdcee45f184c8bd2d9701c56ed009040c2737e40d", "ref_doc_id": "0c07224e-6ec1-465b-9b80-c09dea2feabe"}, "d20e16be-5fea-418e-aaf2-3791aa3c12bb": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "3e2b0089-22d8-4238-9435-28ec5bc9208e"}, "8a7625d0-a2ed-4a7d-8055-fe617fcd980a": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "58c5eea1-2caa-47f5-9672-4b803aabd16b"}, "cbd315ff-b4d2-44a3-80b7-4243b6a256d9": {"doc_hash": "d62c86b626d077f6264d22b389b18391554d74ef8aef0103d5ac5275fecb4db9", "ref_doc_id": "f76068c7-d66a-4d11-bc3b-32e3912ebddd"}, "a7c3d4ad-75a5-4900-8e5c-b9a2f3c5cc81": {"doc_hash": "6ce09459a8021a3b5ad11c1d0908a2460def7cb9af9a5e7a5134d79e0b5b7c54", "ref_doc_id": "39e759f9-b636-4bdd-ab7c-815792a8bb22"}, "3d49d450-09f2-4ce7-a8b7-b1cdf5ca168a": {"doc_hash": "44f2c1fb20902fced65c118d4cc8e32326457581d09ad532e407ca43bc0e14f0", "ref_doc_id": "39e759f9-b636-4bdd-ab7c-815792a8bb22"}, "2c280659-82d9-4216-9f4d-59018d0232cf": {"doc_hash": "96b810418872148f98b65ca5e2a5a15bdda158b93d406487034520ee8b370288", "ref_doc_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e"}, "9b0addc5-3027-400b-a254-a8855f1730a0": {"doc_hash": "a68c9e7612189f5d52b262f6c4cf3a971e8eeecf8cb172b7224434c735e20aaf", "ref_doc_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e"}, "d458dc82-f1fc-4d69-82fe-452ac241c63a": {"doc_hash": "463f247cfa76fbdc18c42e4863b3009dc31e79efc2c6686253e49f7982d73c06", "ref_doc_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e"}, "45ba80ef-530f-4f57-b918-9c6fa6abd3c1": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "44dfbad2-9b28-4b93-9ae0-df0ac22aedd1"}, "b1c97209-182d-4d6d-9bb8-32c9f0ecd96f": {"doc_hash": "e31d50396f9fe91a1de599949b3a251415929a11cff68d89154f361e9cd633ca", "ref_doc_id": "7b2bfec4-6cbe-4613-8837-380ed3ddc797"}, "994edf15-c83e-4d37-aed9-9f6ec23cf167": {"doc_hash": "e854801e662d0b1180e042a92df812b3e1e1d3ce83be067eadc4386ed52f4cff", "ref_doc_id": "c9203a68-cce0-46c1-a2c1-51e84a7bb1a2"}, "eff5e4df-0793-40e1-ad03-a560ef0429bb": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "7b9820a0-ffec-4329-a538-c48faa3da47d"}, "c00c5aa6-b7fd-4b44-8fca-a1777c8aebfa": {"doc_hash": "d490ec7d03c6eb5499e4d3d45e5821b8f28a07fb73f4b8c940c1bd9199bba784", "ref_doc_id": "5624308a-dd90-4869-9795-d3271821f5b4"}, "8a5161c5-2289-44ee-817f-9f1afc6ed21c": {"doc_hash": "bc6d5ddf5ba3d388e797fb61b1b474bd7e969b31036e8ce5ff6f67bfb24c6b3c", "ref_doc_id": "04f73f6f-58aa-4a97-b36d-1a1a9908cb5c"}, "7ef28e7b-066c-4333-a9ae-25df54aae85d": {"doc_hash": "ba9b9155e4928e4e169186d7f93ac7e920b03ff2f38b6c524be7271501423065", "ref_doc_id": "6bda8a9f-4495-4259-a80a-d309d17b54c5"}, "71fa8d31-d5a5-42a8-b66b-e71d659d6261": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "2cae6662-3d5c-4481-82d8-d213e869b0fe"}, "e81ffc88-df02-40c2-9187-6c50cb25fd17": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "e81807cc-d80b-4283-b8c5-d95301e5766c"}, "6d3d5ad2-a3ca-4c02-a806-19c8996b845b": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "922942b3-c87c-49f4-a621-fe86cea29b15"}, "a04e73be-4dda-4a78-bf40-8b19ed85cff4": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "facc5d91-b971-46f8-b8d8-b7f116e56e6d"}, "a3d5f118-31b1-4e0c-86c5-4136880d7a30": {"doc_hash": "02209da8691416a3d7d77a59fc3caea5741b8db37d9e53370217d3272ea3355f", "ref_doc_id": "f22e9609-5bbc-444d-b270-0bf50f3edd33"}, "b660f57d-8afb-4130-93e8-3783047a8c74": {"doc_hash": "fe49f3769ce90b520fe9ebfebd1fa070484a544dcabbbab11f50918a79e91db9", "ref_doc_id": "8a74d797-f340-472c-90b2-2e5901bb2d4a"}, "665436f5-c1f3-4f79-96d7-4f50da74fc6f": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "55829c13-f8ab-45a6-bad1-92162ee79b05"}}, "docstore/ref_doc_info": {"d387c35d-8dc4-43ee-9bbe-c79a3ea4cf08": {"node_ids": ["f67c2943-9099-45ae-b257-4a328a76192b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "f3e3afca-b01c-41cb-aaf8-31f47ac39604": {"node_ids": ["2f6cadcb-19bf-453a-9d37-fdc3f7f36e6e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "cdd913aa-4e50-41b0-b379-3c83cc79a8f7": {"node_ids": ["033ac525-4ddc-4324-af88-7cb68162cfe2"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "0c07224e-6ec1-465b-9b80-c09dea2feabe": {"node_ids": ["121cfa84-2eeb-4775-8029-1a3b35e9cbb6"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "3e2b0089-22d8-4238-9435-28ec5bc9208e": {"node_ids": ["d20e16be-5fea-418e-aaf2-3791aa3c12bb"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "58c5eea1-2caa-47f5-9672-4b803aabd16b": {"node_ids": ["8a7625d0-a2ed-4a7d-8055-fe617fcd980a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "f76068c7-d66a-4d11-bc3b-32e3912ebddd": {"node_ids": ["cbd315ff-b4d2-44a3-80b7-4243b6a256d9"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": false, "attachment_count": 0}}, "39e759f9-b636-4bdd-ab7c-815792a8bb22": {"node_ids": ["a7c3d4ad-75a5-4900-8e5c-b9a2f3c5cc81", "3d49d450-09f2-4ce7-a8b7-b1cdf5ca168a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}}, "2aa38dd9-c60e-47e4-af9f-657958eb5c9e": {"node_ids": ["2c280659-82d9-4216-9f4d-59018d0232cf", "9b0addc5-3027-400b-a254-a8855f1730a0", "d458dc82-f1fc-4d69-82fe-452ac241c63a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}}, "44dfbad2-9b28-4b93-9ae0-df0ac22aedd1": {"node_ids": ["45ba80ef-530f-4f57-b918-9c6fa6abd3c1"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "7b2bfec4-6cbe-4613-8837-380ed3ddc797": {"node_ids": ["b1c97209-182d-4d6d-9bb8-32c9f0ecd96f"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2}}, "c9203a68-cce0-46c1-a2c1-51e84a7bb1a2": {"node_ids": ["994edf15-c83e-4d37-aed9-9f6ec23cf167"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}}, "7b9820a0-ffec-4329-a538-c48faa3da47d": {"node_ids": ["eff5e4df-0793-40e1-ad03-a560ef0429bb"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "5624308a-dd90-4869-9795-d3271821f5b4": {"node_ids": ["c00c5aa6-b7fd-4b44-8fca-a1777c8aebfa"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2}}, "04f73f6f-58aa-4a97-b36d-1a1a9908cb5c": {"node_ids": ["8a5161c5-2289-44ee-817f-9f1afc6ed21c"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": false, "attachment_count": 0}}, "6bda8a9f-4495-4259-a80a-d309d17b54c5": {"node_ids": ["7ef28e7b-066c-4333-a9ae-25df54aae85d"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": false, "attachment_count": 0}}, "2cae6662-3d5c-4481-82d8-d213e869b0fe": {"node_ids": ["71fa8d31-d5a5-42a8-b66b-e71d659d6261"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "e81807cc-d80b-4283-b8c5-d95301e5766c": {"node_ids": ["e81ffc88-df02-40c2-9187-6c50cb25fd17"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "922942b3-c87c-49f4-a621-fe86cea29b15": {"node_ids": ["6d3d5ad2-a3ca-4c02-a806-19c8996b845b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "facc5d91-b971-46f8-b8d8-b7f116e56e6d": {"node_ids": ["a04e73be-4dda-4a78-bf40-8b19ed85cff4"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "f22e9609-5bbc-444d-b270-0bf50f3edd33": {"node_ids": ["a3d5f118-31b1-4e0c-86c5-4136880d7a30"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "8a74d797-f340-472c-90b2-2e5901bb2d4a": {"node_ids": ["b660f57d-8afb-4130-93e8-3783047a8c74"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": false, "attachment_count": 0}}, "55829c13-f8ab-45a6-bad1-92162ee79b05": {"node_ids": ["665436f5-c1f3-4f79-96d7-4f50da74fc6f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"f67c2943-9099-45ae-b257-4a328a76192b": {"__data__": {"id_": "f67c2943-9099-45ae-b257-4a328a76192b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d387c35d-8dc4-43ee-9bbe-c79a3ea4cf08", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2f6cadcb-19bf-453a-9d37-fdc3f7f36e6e": {"__data__": {"id_": "2f6cadcb-19bf-453a-9d37-fdc3f7f36e6e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f3e3afca-b01c-41cb-aaf8-31f47ac39604", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 595, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "033ac525-4ddc-4324-af88-7cb68162cfe2": {"__data__": {"id_": "033ac525-4ddc-4324-af88-7cb68162cfe2", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "cdd913aa-4e50-41b0-b379-3c83cc79a8f7", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "121cfa84-2eeb-4775-8029-1a3b35e9cbb6": {"__data__": {"id_": "121cfa84-2eeb-4775-8029-1a3b35e9cbb6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0c07224e-6ec1-465b-9b80-c09dea2feabe", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d20e16be-5fea-418e-aaf2-3791aa3c12bb": {"__data__": {"id_": "d20e16be-5fea-418e-aaf2-3791aa3c12bb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "3e2b0089-22d8-4238-9435-28ec5bc9208e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8a7625d0-a2ed-4a7d-8055-fe617fcd980a": {"__data__": {"id_": "8a7625d0-a2ed-4a7d-8055-fe617fcd980a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "58c5eea1-2caa-47f5-9672-4b803aabd16b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "cbd315ff-b4d2-44a3-80b7-4243b6a256d9": {"__data__": {"id_": "cbd315ff-b4d2-44a3-80b7-4243b6a256d9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f76068c7-d66a-4d11-bc3b-32e3912ebddd", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": false, "attachment_count": 0}, "hash": "7e90223e411255dbd5f3c8eb5645ec9e73b8f9d1d693366a4e9acc1382562af2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a7c3d4ad-75a5-4900-8e5c-b9a2f3c5cc81": {"__data__": {"id_": "a7c3d4ad-75a5-4900-8e5c-b9a2f3c5cc81", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39e759f9-b636-4bdd-ab7c-815792a8bb22", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}, "hash": "399a0326ef42452699b755d565db2579b884ec8f6db759abc140741531f61003", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "3d49d450-09f2-4ce7-a8b7-b1cdf5ca168a", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3d49d450-09f2-4ce7-a8b7-b1cdf5ca168a": {"__data__": {"id_": "3d49d450-09f2-4ce7-a8b7-b1cdf5ca168a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "39e759f9-b636-4bdd-ab7c-815792a8bb22", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}, "hash": "399a0326ef42452699b755d565db2579b884ec8f6db759abc140741531f61003", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "a7c3d4ad-75a5-4900-8e5c-b9a2f3c5cc81", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": false, "attachment_count": 0}, "hash": "6ce09459a8021a3b5ad11c1d0908a2460def7cb9af9a5e7a5134d79e0b5b7c54", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2c280659-82d9-4216-9f4d-59018d0232cf": {"__data__": {"id_": "2c280659-82d9-4216-9f4d-59018d0232cf", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "hash": "b2a31f86924844dff488930fb5558fd43a77f97ed341ef36bafdf82efae38d36", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "9b0addc5-3027-400b-a254-a8855f1730a0", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9b0addc5-3027-400b-a254-a8855f1730a0": {"__data__": {"id_": "9b0addc5-3027-400b-a254-a8855f1730a0", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "hash": "b2a31f86924844dff488930fb5558fd43a77f97ed341ef36bafdf82efae38d36", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "2c280659-82d9-4216-9f4d-59018d0232cf", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "hash": "96b810418872148f98b65ca5e2a5a15bdda158b93d406487034520ee8b370288", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "d458dc82-f1fc-4d69-82fe-452ac241c63a", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d458dc82-f1fc-4d69-82fe-452ac241c63a": {"__data__": {"id_": "d458dc82-f1fc-4d69-82fe-452ac241c63a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2aa38dd9-c60e-47e4-af9f-657958eb5c9e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "hash": "b2a31f86924844dff488930fb5558fd43a77f97ed341ef36bafdf82efae38d36", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "9b0addc5-3027-400b-a254-a8855f1730a0", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": false, "attachment_count": 0}, "hash": "a68c9e7612189f5d52b262f6c4cf3a971e8eeecf8cb172b7224434c735e20aaf", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "45ba80ef-530f-4f57-b918-9c6fa6abd3c1": {"__data__": {"id_": "45ba80ef-530f-4f57-b918-9c6fa6abd3c1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "44dfbad2-9b28-4b93-9ae0-df0ac22aedd1", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b1c97209-182d-4d6d-9bb8-32c9f0ecd96f": {"__data__": {"id_": "b1c97209-182d-4d6d-9bb8-32c9f0ecd96f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7b2bfec4-6cbe-4613-8837-380ed3ddc797", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2}, "hash": "1496e48148b147e18519f2a7029b44051bfcf510422e148b1705c913d11ce852", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: Manual_GP-PWM-10-FM.pdf\nFile Type: application/pdf\nSize: 2054139 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 119, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "994edf15-c83e-4d37-aed9-9f6ec23cf167": {"__data__": {"id_": "994edf15-c83e-4d37-aed9-9f6ec23cf167", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c9203a68-cce0-46c1-a2c1-51e84a7bb1a2", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "hash": "56fdc6f005521788f7b25d646a50785c20f425413e05377c714665776ab8b066", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle  \n  \nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1200, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "eff5e4df-0793-40e1-ad03-a560ef0429bb": {"__data__": {"id_": "eff5e4df-0793-40e1-ad03-a560ef0429bb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7b9820a0-ffec-4329-a538-c48faa3da47d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c00c5aa6-b7fd-4b44-8fca-a1777c8aebfa": {"__data__": {"id_": "c00c5aa6-b7fd-4b44-8fca-a1777c8aebfa", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5624308a-dd90-4869-9795-d3271821f5b4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2}, "hash": "c621b9db2852ee24de749a386a579f8cd218deb31c5b4676d904686cfc871309", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 125, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8a5161c5-2289-44ee-817f-9f1afc6ed21c": {"__data__": {"id_": "8a5161c5-2289-44ee-817f-9f1afc6ed21c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "04f73f6f-58aa-4a97-b36d-1a1a9908cb5c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": false, "attachment_count": 0}, "hash": "bdf6b271c547142ecb31404b67156e3e30918f1aba4d14211e83ba0855fd3200", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7ef28e7b-066c-4333-a9ae-25df54aae85d": {"__data__": {"id_": "7ef28e7b-066c-4333-a9ae-25df54aae85d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6bda8a9f-4495-4259-a80a-d309d17b54c5", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": false, "attachment_count": 0}, "hash": "b5e432fec16ec49695de556d9e4b9c99f23a9c24cd4712aa446ba9d980d0c270", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "71fa8d31-d5a5-42a8-b66b-e71d659d6261": {"__data__": {"id_": "71fa8d31-d5a5-42a8-b66b-e71d659d6261", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2cae6662-3d5c-4481-82d8-d213e869b0fe", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e81ffc88-df02-40c2-9187-6c50cb25fd17": {"__data__": {"id_": "e81ffc88-df02-40c2-9187-6c50cb25fd17", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e81807cc-d80b-4283-b8c5-d95301e5766c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6d3d5ad2-a3ca-4c02-a806-19c8996b845b": {"__data__": {"id_": "6d3d5ad2-a3ca-4c02-a806-19c8996b845b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "922942b3-c87c-49f4-a621-fe86cea29b15", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a04e73be-4dda-4a78-bf40-8b19ed85cff4": {"__data__": {"id_": "a04e73be-4dda-4a78-bf40-8b19ed85cff4", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "facc5d91-b971-46f8-b8d8-b7f116e56e6d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a3d5f118-31b1-4e0c-86c5-4136880d7a30": {"__data__": {"id_": "a3d5f118-31b1-4e0c-86c5-4136880d7a30", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f22e9609-5bbc-444d-b270-0bf50f3edd33", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b660f57d-8afb-4130-93e8-3783047a8c74": {"__data__": {"id_": "b660f57d-8afb-4130-93e8-3783047a8c74", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8a74d797-f340-472c-90b2-2e5901bb2d4a", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": false, "attachment_count": 0}, "hash": "3def1c6e187688949579135fb1ffb766200356e66a7a6e2ae4ed92810a293916", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "665436f5-c1f3-4f79-96d7-4f50da74fc6f": {"__data__": {"id_": "665436f5-c1f3-4f79-96d7-4f50da74fc6f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "55829c13-f8ab-45a6-bad1-92162ee79b05", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}