{"docstore/metadata": {"739c41d8-954a-4396-b811-ded8ba785234": {"doc_hash": "88766fa21828e9abf4af3c576f4d4d65c5bfdac34d2193c39084c7bf2ce4a7e9"}, "405c3537-0d74-47e6-be51-a90d57746e06": {"doc_hash": "1333621e1c5691e95023046d111641374b692833e4b661f6b698a7e5c375fd54"}, "60de4a11-d895-44e2-8730-11ae424efab5": {"doc_hash": "0ab0dce6bdfcfbab211523681e2c952bff5d778938db8521e6a5aa24619202b1"}, "9e956dae-0033-45dd-b19f-974f0c43a067": {"doc_hash": "f23aa940c3f0fa9a676b4dbd412498517ad60071a95021489f699be6192889a7"}, "c68d164d-1d8e-4fa6-9c1c-ddfd58b10de9": {"doc_hash": "09456673a3446a7cd8cd790d5d0a963ec10110af9263c357a19d8f04bcb82aa8"}, "fce5f882-0fc9-42d2-8a31-712de6df98b4": {"doc_hash": "ecc568f0eaf95867e87b9c4e3dbbc15457b2519cb9520efefb724b3e6e5ff8bd"}, "e7ff041d-e352-47a5-ab96-f8f68f647d08": {"doc_hash": "dd740db3fdfe708e535070a51d5fdb52522596831a7245b9a87e00811c2fd33a"}, "09d6473c-ae36-4096-a9bb-515da9975d4d": {"doc_hash": "398929eff9308574bc6300adfb60f697c770de695486194e43bd1c51734750d2"}, "903b22ff-ae1f-4207-a30e-ed620b2047a4": {"doc_hash": "5c3833b3cc9db108e04808258a1a6e540df7dfe66189574d46582a80dd810815"}, "5caa5e82-9524-422a-80c0-7b693e66129d": {"doc_hash": "cf675198236b24e16e041e11a80c6880d2e446eb89a93653aceef434bfda20de"}, "c66a20d2-a758-4e23-94f7-e593a4163a7e": {"doc_hash": "bfece1c7f8c8ca4ca7090ebadd3ad4588a010f78525867592342dd31cd16ad97"}, "e5a61f55-682f-4354-a4cc-03dbecdb8375": {"doc_hash": "2cc79eb780ca95ff595fa1450570b117503f3ab83bac9d346b37bbc91c439224"}, "e98dc4aa-3357-422b-8410-26dda4269e12": {"doc_hash": "46536ce6b91e67e693075283d3eeb9e4ec1c5982e32efb60105eb7e676a033c6"}, "24088d3f-0f2c-4aaf-9cec-54b7c13fc477": {"doc_hash": "311e24a6d4b4fee94fea68569070ab03499fdf94ac7b9fbb7292481107fe92d4"}, "79257a71-d57f-4b2c-b09d-345ac43e652f": {"doc_hash": "e8af0edf66a4b9bfa9c32026640916b9987805b9793f9fda22a03210606760bb"}, "9fc8a5bd-8044-4a13-ae76-ec64df80e159": {"doc_hash": "9216744d120004c5154b26ff5213edb705b1f6303a36dedb3374cd7931c606a6"}, "********-2a3d-44bc-801f-7f833440c294": {"doc_hash": "d89b7c92f446a26244bb61f57b54adc65449cb19e8eed35eed87fcfc2d798d3e"}, "a1a47e8d-fa26-4e84-939a-88d26f0525d9": {"doc_hash": "a95cf0de7d0b0d387bdaf5bd5c573e43695b0618422293149e938edbc063e1e4"}, "97be7ccc-27f9-44b1-b50c-ea79e408a679": {"doc_hash": "520f45cfa34ef8dff6e7a2471ef3f8839a9aaf0c1bc173826a0541449390d456"}, "4ca02508-664c-4a77-b637-9fcba3823510": {"doc_hash": "4fd959744a1b9700f301ffcd028903ca75213c2b4e6ebb501a23e80c7d75c75c"}, "232ae7ef-7f3c-411d-99d2-63027871b2de": {"doc_hash": "ae93135cb999e3c5bb7945f8776226d9c503e5ef9303f40c54be7311b63f4492", "ref_doc_id": "739c41d8-954a-4396-b811-ded8ba785234"}, "8e3eb15e-a861-4183-be1d-b91b15b76a50": {"doc_hash": "a58176d8edeefe4e8363daa9d47f909de7102ebc75527cc3a1202ad1ea44c654", "ref_doc_id": "405c3537-0d74-47e6-be51-a90d57746e06"}, "975ac900-0b76-4e63-bb6d-9296fb87ab3a": {"doc_hash": "3eca5e162134f4ad87d96ec1956e178fd3538285cbb8d59aa596c6cba1a88b39", "ref_doc_id": "60de4a11-d895-44e2-8730-11ae424efab5"}, "05d1e797-c004-4c49-9367-f41bc095e35f": {"doc_hash": "bb9f71e041e5a7379c9bd1049cc2a2c362729c8731e4b87257f31a6f02501031", "ref_doc_id": "9e956dae-0033-45dd-b19f-974f0c43a067"}, "70eae504-b811-46e9-b525-0d48d10f0a33": {"doc_hash": "48cdd3353bc048ccb33ca7e9618df6cf96c3505fd50e94f4d6f12dce67448b4a", "ref_doc_id": "c68d164d-1d8e-4fa6-9c1c-ddfd58b10de9"}, "e4d03b08-81a7-4f67-9069-dbfeebc45feb": {"doc_hash": "ade044869b27f4c6d93bfb301840a4f0ad3f304323d404ec98b257b620433668", "ref_doc_id": "fce5f882-0fc9-42d2-8a31-712de6df98b4"}, "b96cd1f0-e18e-45fc-a197-c0774e1891f2": {"doc_hash": "23de1bea2c7da88cb4a644a48292d79ad67b8f8bc440aa4e8afca6b09871f689", "ref_doc_id": "e7ff041d-e352-47a5-ab96-f8f68f647d08"}, "cd2f60e1-d5c2-414e-bc0e-8de3026da7b5": {"doc_hash": "f035fac55eab96d952ec55e91538fdf09a62604a7ccb83c217c71a79cf890d9d", "ref_doc_id": "09d6473c-ae36-4096-a9bb-515da9975d4d"}, "8ef0c0a9-772e-4202-9ee5-5f7eee4e81a6": {"doc_hash": "d743b0ca718e42f36a9bda6e1288bdda6836b448fc215500b40289c305b236d7", "ref_doc_id": "09d6473c-ae36-4096-a9bb-515da9975d4d"}, "5d5a524b-c638-4761-91ca-625c880c24ef": {"doc_hash": "065875a2a45da6558bc1c7dda7d078f947e31924d0122455fff47562df9758f5", "ref_doc_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4"}, "4b12014a-2227-480f-bd0b-e5f33a89a53c": {"doc_hash": "f0e56aa3daf25d8017bcb255cb81b79096da7e85da0f056c05e3304a68b97abe", "ref_doc_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4"}, "1e61f02c-e0e8-4adb-a48b-16c2d3861b8e": {"doc_hash": "e796641e373d92c27443762bf9f4b6d607c7d8c5c92c5cc8e0301a22a36ec3b7", "ref_doc_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4"}, "d040d25a-767f-4210-9a85-542b13314e8d": {"doc_hash": "98beec834854c56e94be459a5b316337dbcf9d85a98234df536c96a864e9a1d9", "ref_doc_id": "5caa5e82-9524-422a-80c0-7b693e66129d"}, "2ab42036-d8fd-4702-9d19-f9b569b8d12c": {"doc_hash": "1be9bb3697fb7521485cade7044f024c6f71109701082a87b402831b6ac9ca31", "ref_doc_id": "c66a20d2-a758-4e23-94f7-e593a4163a7e"}, "74f711cf-8ec9-421d-8fc9-1c0450e450e1": {"doc_hash": "2188c280efaa4b481d2b9e776a4c49ce8a0123680e703d38390c03a6b0318a66", "ref_doc_id": "e5a61f55-682f-4354-a4cc-03dbecdb8375"}, "e73698bd-1434-4189-8acc-cd233647cd27": {"doc_hash": "68b334b619a39ef2e0bb17e2b7495932a3c5071bfa48259bae05505e4c4f1a5a", "ref_doc_id": "e98dc4aa-3357-422b-8410-26dda4269e12"}, "d2aabdd3-c538-4bee-ace9-1526fe67f96f": {"doc_hash": "60f847e50b2a7082a436f083c4ee2e3925d3b203ba09ac06bb18d2d592b0c059", "ref_doc_id": "24088d3f-0f2c-4aaf-9cec-54b7c13fc477"}, "9b969cee-2eee-4e88-9a7a-41a81a087396": {"doc_hash": "7d74a22f0054a60288f42ad79fb2f1395b4bed7592a9975d02d26d270d787016", "ref_doc_id": "79257a71-d57f-4b2c-b09d-345ac43e652f"}, "dbf052bc-de78-475d-a28c-6138e66de30a": {"doc_hash": "f04ff744f26aceac543747bf5766dd1a5e18a84025e723147cd027531ff34717", "ref_doc_id": "9fc8a5bd-8044-4a13-ae76-ec64df80e159"}, "********-141a-45c1-8573-2b248890da61": {"doc_hash": "74c6e0776dc590d005dbba04ee2756e48b05dd848167a3039c25865715931c98", "ref_doc_id": "********-2a3d-44bc-801f-7f833440c294"}, "1d4aadf2-e6ac-4b3b-ba97-fb4186964693": {"doc_hash": "c75919a5a77a71fdad51b01617b5f055a4e0c64ec57ab7d3649f68ed90e1e217", "ref_doc_id": "a1a47e8d-fa26-4e84-939a-88d26f0525d9"}, "ca4cf29c-7752-4c87-b5c9-ec33e2491867": {"doc_hash": "60f4c240ec35c1dfd9fdcb9b4383eaa4866c6d8ea69e3b3d0bc7f944024139b4", "ref_doc_id": "97be7ccc-27f9-44b1-b50c-ea79e408a679"}, "be9d3aa3-3f6b-4d99-8ba2-9230c913e867": {"doc_hash": "8b47a5f430ef31f8edb2cf0dedc7ef6786af716c7d379d65a7615f0c8dc22fae", "ref_doc_id": "4ca02508-664c-4a77-b637-9fcba3823510"}}, "docstore/ref_doc_info": {"739c41d8-954a-4396-b811-ded8ba785234": {"node_ids": ["232ae7ef-7f3c-411d-99d2-63027871b2de"], "metadata": {"source": "HappyFox", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z"}}, "405c3537-0d74-47e6-be51-a90d57746e06": {"node_ids": ["8e3eb15e-a861-4183-be1d-b91b15b76a50"], "metadata": {"source": "HappyFox", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z"}}, "60de4a11-d895-44e2-8730-11ae424efab5": {"node_ids": ["975ac900-0b76-4e63-bb6d-9296fb87ab3a"], "metadata": {"source": "HappyFox", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z"}}, "9e956dae-0033-45dd-b19f-974f0c43a067": {"node_ids": ["05d1e797-c004-4c49-9367-f41bc095e35f"], "metadata": {"source": "HappyFox", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z"}}, "c68d164d-1d8e-4fa6-9c1c-ddfd58b10de9": {"node_ids": ["70eae504-b811-46e9-b525-0d48d10f0a33"], "metadata": {"source": "HappyFox", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z"}}, "fce5f882-0fc9-42d2-8a31-712de6df98b4": {"node_ids": ["e4d03b08-81a7-4f67-9069-dbfeebc45feb"], "metadata": {"source": "HappyFox", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z"}}, "e7ff041d-e352-47a5-ab96-f8f68f647d08": {"node_ids": ["b96cd1f0-e18e-45fc-a197-c0774e1891f2"], "metadata": {"source": "HappyFox", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z"}}, "09d6473c-ae36-4096-a9bb-515da9975d4d": {"node_ids": ["cd2f60e1-d5c2-414e-bc0e-8de3026da7b5", "8ef0c0a9-772e-4202-9ee5-5f7eee4e81a6"], "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}}, "903b22ff-ae1f-4207-a30e-ed620b2047a4": {"node_ids": ["5d5a524b-c638-4761-91ca-625c880c24ef", "4b12014a-2227-480f-bd0b-e5f33a89a53c", "1e61f02c-e0e8-4adb-a48b-16c2d3861b8e"], "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}}, "5caa5e82-9524-422a-80c0-7b693e66129d": {"node_ids": ["d040d25a-767f-4210-9a85-542b13314e8d"], "metadata": {"source": "HappyFox", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z"}}, "c66a20d2-a758-4e23-94f7-e593a4163a7e": {"node_ids": ["2ab42036-d8fd-4702-9d19-f9b569b8d12c"], "metadata": {"source": "HappyFox", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z"}}, "e5a61f55-682f-4354-a4cc-03dbecdb8375": {"node_ids": ["74f711cf-8ec9-421d-8fc9-1c0450e450e1"], "metadata": {"source": "HappyFox", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z"}}, "e98dc4aa-3357-422b-8410-26dda4269e12": {"node_ids": ["e73698bd-1434-4189-8acc-cd233647cd27"], "metadata": {"source": "HappyFox", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z"}}, "24088d3f-0f2c-4aaf-9cec-54b7c13fc477": {"node_ids": ["d2aabdd3-c538-4bee-ace9-1526fe67f96f"], "metadata": {"source": "HappyFox", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z"}}, "79257a71-d57f-4b2c-b09d-345ac43e652f": {"node_ids": ["9b969cee-2eee-4e88-9a7a-41a81a087396"], "metadata": {"source": "HappyFox", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z"}}, "9fc8a5bd-8044-4a13-ae76-ec64df80e159": {"node_ids": ["dbf052bc-de78-475d-a28c-6138e66de30a"], "metadata": {"source": "HappyFox", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z"}}, "********-2a3d-44bc-801f-7f833440c294": {"node_ids": ["********-141a-45c1-8573-2b248890da61"], "metadata": {"source": "HappyFox", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z"}}, "a1a47e8d-fa26-4e84-939a-88d26f0525d9": {"node_ids": ["1d4aadf2-e6ac-4b3b-ba97-fb4186964693"], "metadata": {"source": "HappyFox", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z"}}, "97be7ccc-27f9-44b1-b50c-ea79e408a679": {"node_ids": ["ca4cf29c-7752-4c87-b5c9-ec33e2491867"], "metadata": {"source": "HappyFox", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z"}}, "4ca02508-664c-4a77-b637-9fcba3823510": {"node_ids": ["be9d3aa3-3f6b-4d99-8ba2-9230c913e867"], "metadata": {"source": "HappyFox", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z"}}}, "docstore/data": {"232ae7ef-7f3c-411d-99d2-63027871b2de": {"__data__": {"id_": "232ae7ef-7f3c-411d-99d2-63027871b2de", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "739c41d8-954a-4396-b811-ded8ba785234", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z"}, "hash": "88766fa21828e9abf4af3c576f4d4d65c5bfdac34d2193c39084c7bf2ce4a7e9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8e3eb15e-a861-4183-be1d-b91b15b76a50": {"__data__": {"id_": "8e3eb15e-a861-4183-be1d-b91b15b76a50", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "405c3537-0d74-47e6-be51-a90d57746e06", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z"}, "hash": "1333621e1c5691e95023046d111641374b692833e4b661f6b698a7e5c375fd54", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:  \n  \n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 599, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "975ac900-0b76-4e63-bb6d-9296fb87ab3a": {"__data__": {"id_": "975ac900-0b76-4e63-bb6d-9296fb87ab3a", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "60de4a11-d895-44e2-8730-11ae424efab5", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z"}, "hash": "0ab0dce6bdfcfbab211523681e2c952bff5d778938db8521e6a5aa24619202b1", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "05d1e797-c004-4c49-9367-f41bc095e35f": {"__data__": {"id_": "05d1e797-c004-4c49-9367-f41bc095e35f", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9e956dae-0033-45dd-b19f-974f0c43a067", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z"}, "hash": "f23aa940c3f0fa9a676b4dbd412498517ad60071a95021489f699be6192889a7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "70eae504-b811-46e9-b525-0d48d10f0a33": {"__data__": {"id_": "70eae504-b811-46e9-b525-0d48d10f0a33", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c68d164d-1d8e-4fa6-9c1c-ddfd58b10de9", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z"}, "hash": "09456673a3446a7cd8cd790d5d0a963ec10110af9263c357a19d8f04bcb82aa8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e4d03b08-81a7-4f67-9069-dbfeebc45feb": {"__data__": {"id_": "e4d03b08-81a7-4f67-9069-dbfeebc45feb", "embedding": null, "metadata": {"source": "HappyFox", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fce5f882-0fc9-42d2-8a31-712de6df98b4", "node_type": "4", "metadata": {"source": "HappyFox", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z"}, "hash": "ecc568f0eaf95867e87b9c4e3dbbc15457b2519cb9520efefb724b3e6e5ff8bd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "b96cd1f0-e18e-45fc-a197-c0774e1891f2": {"__data__": {"id_": "b96cd1f0-e18e-45fc-a197-c0774e1891f2", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e7ff041d-e352-47a5-ab96-f8f68f647d08", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z"}, "hash": "dd740db3fdfe708e535070a51d5fdb52522596831a7245b9a87e00811c2fd33a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "cd2f60e1-d5c2-414e-bc0e-8de3026da7b5": {"__data__": {"id_": "cd2f60e1-d5c2-414e-bc0e-8de3026da7b5", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "09d6473c-ae36-4096-a9bb-515da9975d4d", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}, "hash": "398929eff9308574bc6300adfb60f697c770de695486194e43bd1c51734750d2", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "8ef0c0a9-772e-4202-9ee5-5f7eee4e81a6", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8ef0c0a9-772e-4202-9ee5-5f7eee4e81a6": {"__data__": {"id_": "8ef0c0a9-772e-4202-9ee5-5f7eee4e81a6", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "09d6473c-ae36-4096-a9bb-515da9975d4d", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}, "hash": "398929eff9308574bc6300adfb60f697c770de695486194e43bd1c51734750d2", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "cd2f60e1-d5c2-414e-bc0e-8de3026da7b5", "node_type": "1", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z"}, "hash": "f035fac55eab96d952ec55e91538fdf09a62604a7ccb83c217c71a79cf890d9d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5d5a524b-c638-4761-91ca-625c880c24ef": {"__data__": {"id_": "5d5a524b-c638-4761-91ca-625c880c24ef", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "hash": "5c3833b3cc9db108e04808258a1a6e540df7dfe66189574d46582a80dd810815", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "4b12014a-2227-480f-bd0b-e5f33a89a53c", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4b12014a-2227-480f-bd0b-e5f33a89a53c": {"__data__": {"id_": "4b12014a-2227-480f-bd0b-e5f33a89a53c", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "hash": "5c3833b3cc9db108e04808258a1a6e540df7dfe66189574d46582a80dd810815", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "5d5a524b-c638-4761-91ca-625c880c24ef", "node_type": "1", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "hash": "065875a2a45da6558bc1c7dda7d078f947e31924d0122455fff47562df9758f5", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "1e61f02c-e0e8-4adb-a48b-16c2d3861b8e", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1e61f02c-e0e8-4adb-a48b-16c2d3861b8e": {"__data__": {"id_": "1e61f02c-e0e8-4adb-a48b-16c2d3861b8e", "embedding": null, "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "903b22ff-ae1f-4207-a30e-ed620b2047a4", "node_type": "4", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "hash": "5c3833b3cc9db108e04808258a1a6e540df7dfe66189574d46582a80dd810815", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "4b12014a-2227-480f-bd0b-e5f33a89a53c", "node_type": "1", "metadata": {"source": "HappyFox", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z"}, "hash": "f0e56aa3daf25d8017bcb255cb81b79096da7e85da0f056c05e3304a68b97abe", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d040d25a-767f-4210-9a85-542b13314e8d": {"__data__": {"id_": "d040d25a-767f-4210-9a85-542b13314e8d", "embedding": null, "metadata": {"source": "HappyFox", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5caa5e82-9524-422a-80c0-7b693e66129d", "node_type": "4", "metadata": {"source": "HappyFox", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z"}, "hash": "cf675198236b24e16e041e11a80c6880d2e446eb89a93653aceef434bfda20de", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle\n\nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1196, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2ab42036-d8fd-4702-9d19-f9b569b8d12c": {"__data__": {"id_": "2ab42036-d8fd-4702-9d19-f9b569b8d12c", "embedding": null, "metadata": {"source": "HappyFox", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c66a20d2-a758-4e23-94f7-e593a4163a7e", "node_type": "4", "metadata": {"source": "HappyFox", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z"}, "hash": "bfece1c7f8c8ca4ca7090ebadd3ad4588a010f78525867592342dd31cd16ad97", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "74f711cf-8ec9-421d-8fc9-1c0450e450e1": {"__data__": {"id_": "74f711cf-8ec9-421d-8fc9-1c0450e450e1", "embedding": null, "metadata": {"source": "HappyFox", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e5a61f55-682f-4354-a4cc-03dbecdb8375", "node_type": "4", "metadata": {"source": "HappyFox", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z"}, "hash": "2cc79eb780ca95ff595fa1450570b117503f3ab83bac9d346b37bbc91c439224", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e73698bd-1434-4189-8acc-cd233647cd27": {"__data__": {"id_": "e73698bd-1434-4189-8acc-cd233647cd27", "embedding": null, "metadata": {"source": "HappyFox", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e98dc4aa-3357-422b-8410-26dda4269e12", "node_type": "4", "metadata": {"source": "HappyFox", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z"}, "hash": "46536ce6b91e67e693075283d3eeb9e4ec1c5982e32efb60105eb7e676a033c6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d2aabdd3-c538-4bee-ace9-1526fe67f96f": {"__data__": {"id_": "d2aabdd3-c538-4bee-ace9-1526fe67f96f", "embedding": null, "metadata": {"source": "HappyFox", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "24088d3f-0f2c-4aaf-9cec-54b7c13fc477", "node_type": "4", "metadata": {"source": "HappyFox", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z"}, "hash": "311e24a6d4b4fee94fea68569070ab03499fdf94ac7b9fbb7292481107fe92d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9b969cee-2eee-4e88-9a7a-41a81a087396": {"__data__": {"id_": "9b969cee-2eee-4e88-9a7a-41a81a087396", "embedding": null, "metadata": {"source": "HappyFox", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "79257a71-d57f-4b2c-b09d-345ac43e652f", "node_type": "4", "metadata": {"source": "HappyFox", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z"}, "hash": "e8af0edf66a4b9bfa9c32026640916b9987805b9793f9fda22a03210606760bb", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dbf052bc-de78-475d-a28c-6138e66de30a": {"__data__": {"id_": "dbf052bc-de78-475d-a28c-6138e66de30a", "embedding": null, "metadata": {"source": "HappyFox", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9fc8a5bd-8044-4a13-ae76-ec64df80e159", "node_type": "4", "metadata": {"source": "HappyFox", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z"}, "hash": "9216744d120004c5154b26ff5213edb705b1f6303a36dedb3374cd7931c606a6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "********-141a-45c1-8573-2b248890da61": {"__data__": {"id_": "********-141a-45c1-8573-2b248890da61", "embedding": null, "metadata": {"source": "HappyFox", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "********-2a3d-44bc-801f-7f833440c294", "node_type": "4", "metadata": {"source": "HappyFox", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z"}, "hash": "d89b7c92f446a26244bb61f57b54adc65449cb19e8eed35eed87fcfc2d798d3e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1d4aadf2-e6ac-4b3b-ba97-fb4186964693": {"__data__": {"id_": "1d4aadf2-e6ac-4b3b-ba97-fb4186964693", "embedding": null, "metadata": {"source": "HappyFox", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a1a47e8d-fa26-4e84-939a-88d26f0525d9", "node_type": "4", "metadata": {"source": "HappyFox", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z"}, "hash": "a95cf0de7d0b0d387bdaf5bd5c573e43695b0618422293149e938edbc063e1e4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ca4cf29c-7752-4c87-b5c9-ec33e2491867": {"__data__": {"id_": "ca4cf29c-7752-4c87-b5c9-ec33e2491867", "embedding": null, "metadata": {"source": "HappyFox", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "97be7ccc-27f9-44b1-b50c-ea79e408a679", "node_type": "4", "metadata": {"source": "HappyFox", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z"}, "hash": "520f45cfa34ef8dff6e7a2471ef3f8839a9aaf0c1bc173826a0541449390d456", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "be9d3aa3-3f6b-4d99-8ba2-9230c913e867": {"__data__": {"id_": "be9d3aa3-3f6b-4d99-8ba2-9230c913e867", "embedding": null, "metadata": {"source": "HappyFox", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z"}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4ca02508-664c-4a77-b637-9fcba3823510", "node_type": "4", "metadata": {"source": "HappyFox", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z"}, "hash": "4fd959744a1b9700f301ffcd028903ca75213c2b4e6ebb501a23e80c7d75c75c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}