{"docstore/metadata": {"7f893863-704b-4453-bc49-26d48832a498": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "bd0e5fd4-8ae0-4fbc-a521-51198f5363d4": {"doc_hash": "7c7241637b1d7da91e846ef2128df0b632edd4db3203d5d7dfef8d7d0d23e708"}, "1d76eb40-8aa5-4a4e-b8ac-6a8fc1ef98d0": {"doc_hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997"}, "f398f4a1-3058-449e-ba2c-95e6f2ad0c9f": {"doc_hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921"}, "e452d632-f45d-478e-95c3-d10f9d5ce142": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "8174075a-88ad-4260-bc42-71aa3794c1af": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "b683e9d5-54b7-4f44-8958-c9055d957fb0": {"doc_hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a"}, "530750fb-63cd-4ab1-a60f-99bfaaca6b9d": {"doc_hash": "fdce9221b7a51d45cd8a679d5ccc41df9957c16c11e87d3652b998375d975c24"}, "5b8a8c91-7297-4b37-b2a3-117914f12d6c": {"doc_hash": "49209c9694101b201ed22f1ed8ed62e41d7cf1e173b99f54510ced22599d19f8"}, "a00ea1ca-2bc9-4940-89c6-e5d57caee37d": {"doc_hash": "8b73ff8299f36ef7a0f1b255fa61f42525982338ba9d3462bc509c1be6a005c3"}, "95687766-130d-47d4-a8ed-f12599585a27": {"doc_hash": "a6165c4eabf5010a45884ca99b9996d2398d5b279ae470d7719b86a9ad1fe54d"}, "d5b0cb0f-364f-4970-ba74-b9fdf7a77022": {"doc_hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f"}, "06ff538f-b88e-4dd4-823e-715c87189871": {"doc_hash": "83e2d625d4eff94277d4c5973581dfd954d6fe91c22d4b6b62e9d280732fdcd9"}, "4502b03d-3484-4ac3-82a8-023d5f2f049d": {"doc_hash": "457ca68bef1ae04b512aa529cafe4e5f6f6e8df48959d2decbada8591bbbbf40"}, "16c055c8-ff6c-4de0-bab6-63170fd073e8": {"doc_hash": "962cbba905397a791d2edc7f5500aa8c000853ce35b1064dacfef269aacad541"}, "038c977a-fa74-48c3-8e9b-544ac81a6491": {"doc_hash": "533092f9d41aeaee314e7236ebd35074574d2d8911229b33ac16516cb3eb8174"}, "a773b1cc-0970-4eac-b07d-902a0af28368": {"doc_hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d"}, "f6cc6728-01b2-4874-8ab8-168515ceb3c6": {"doc_hash": "c2428cc42d96e259987a8f3b01a481a39b9366ea5875414b8a8efb7c7c4c4505"}, "34a3addc-3534-41ad-98fc-45181b95e65d": {"doc_hash": "0d969717dafc28c81d5ab77bb32c364b168179189b8c3db4dc04e2548b9b1e56"}, "308e24bd-f4ca-4081-99a5-48db6f74061b": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "1bff341c-93b4-4ff7-a111-85510f8e6d0f": {"doc_hash": "c55f0a5aab0689358f5954f6fa536cdd563c46835de350432317d86fe7b33dba"}, "6f7a3f75-888f-48cf-b4b5-7b49d9ee5c11": {"doc_hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae"}, "07027a88-1f43-4556-a861-06d2cf2ab78f": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "30668e16-a179-400f-84fa-d888c38aab79": {"doc_hash": "03e8bf0aecb64f104f16037b055fd01bdda109d8b8c8ba456c7e5cccb2f4562b"}, "45f27d6e-0f6b-4b17-8e10-020dba0adcdd": {"doc_hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b"}, "2265b92f-320e-411b-a56b-254e313ee1b4": {"doc_hash": "56134e9235b004e89f0701cabd2e3b6d714866b8714a33aa44049a3eb530a11d"}, "511dc8d6-50c3-4721-ae8d-ae9de0a5ed48": {"doc_hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b"}, "d13c098d-0a08-403e-b739-22e3c89df0df": {"doc_hash": "6a4c596cd168836f508a796aebf90eaa3a7a4d2f97d09f07b1d0b2e2733c0b5a"}, "92705802-9679-4d99-9989-4555a37bee5f": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "1cbf6cd9-c5ba-4535-ae96-d9ac17bb37a8": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "d0621599-d307-4ae9-8747-5a521c4efe8d": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "1512a218-0024-4a6d-a1ae-04e6b82dc116": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "edbe15b6-2f55-4fb8-a3bb-a227be57cfd0": {"doc_hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2"}, "8c12996a-909c-46d9-a107-a918cdb59b37": {"doc_hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98"}, "b92600d8-81a7-473f-b79a-4361005b6ac4": {"doc_hash": "8c3809290019e7bbe83bdc51237895f769ee73bd21a42b0f346c7158c95f0a52"}, "ed5e8e84-863f-4798-b331-70b75e17c1b5": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "51c0cfa8-1ce5-4a5a-b1c9-b6726e5f8e6a": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "7f893863-704b-4453-bc49-26d48832a498"}, "157c4728-368a-4b8d-955c-a4442e908ee3": {"doc_hash": "c30b3433fed3f65e262b0551c7403e465ebf3fa4ee71953be95ea2872286a665", "ref_doc_id": "bd0e5fd4-8ae0-4fbc-a521-51198f5363d4"}, "0f75fd8f-49ea-45f7-baa8-c5f69c685bb4": {"doc_hash": "e8076cdbf063a169a85606ab841d0b0dbe7f0ae8d5923f8d17cbadc695115ebc", "ref_doc_id": "1d76eb40-8aa5-4a4e-b8ac-6a8fc1ef98d0"}, "1d0b24b3-7347-4547-a32e-92d5c44e7f8c": {"doc_hash": "d5252924f1f1e32df134e8dbdcee45f184c8bd2d9701c56ed009040c2737e40d", "ref_doc_id": "f398f4a1-3058-449e-ba2c-95e6f2ad0c9f"}, "e812bc57-40d2-4406-987f-5f076f91a378": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "e452d632-f45d-478e-95c3-d10f9d5ce142"}, "90b5fc87-aa57-4c3d-91a0-09e5a9673bb8": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "8174075a-88ad-4260-bc42-71aa3794c1af"}, "fcac93fb-74a2-43ef-b003-cd6dec616469": {"doc_hash": "5f68e1b4f5c3568fdf5c92b7b5568a53fe10ad316ef1a1b4467f212b2dda586e", "ref_doc_id": "b683e9d5-54b7-4f44-8958-c9055d957fb0"}, "4ac3261b-1736-480c-8dad-19e12ea152ef": {"doc_hash": "7aec3c06a236e4d21e9fc3a44843ff7a881c3b257f306f118246d882311c6cfe", "ref_doc_id": "530750fb-63cd-4ab1-a60f-99bfaaca6b9d"}, "7e07ca04-3897-4eb0-a7c8-5c62269c3874": {"doc_hash": "a1989e20f2764b0b87ea8a3fda891195da70f79ccccd8bbd41325d3340e650bc", "ref_doc_id": "5b8a8c91-7297-4b37-b2a3-117914f12d6c"}, "4e23fc6e-1c76-4d3b-8127-8507b0be512c": {"doc_hash": "5f4dd2abdefd7fc5061981fe7bbb37cd617b6ae4eba960ab250309b50523619b", "ref_doc_id": "a00ea1ca-2bc9-4940-89c6-e5d57caee37d"}, "0bd95c47-4df0-4c17-b0d6-4e654b8b4701": {"doc_hash": "6c15bc58820520458425a2b57388161a18d653a4a6c6f3e9d80e9640c428fe77", "ref_doc_id": "95687766-130d-47d4-a8ed-f12599585a27"}, "38de33d5-0393-4ab6-aaeb-f4f2f00dde69": {"doc_hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "ref_doc_id": "d5b0cb0f-364f-4970-ba74-b9fdf7a77022"}, "341bbfa6-83fa-49ad-9d55-a1b7bad877bc": {"doc_hash": "006639b0301245b1a65425b30dd201c43067eea5724110d5bae59e4512840d8f", "ref_doc_id": "d5b0cb0f-364f-4970-ba74-b9fdf7a77022"}, "86ff191a-3e29-48dc-9175-c939c5ceea1d": {"doc_hash": "725e36788c105cc53fd829c0bdb3b13a110c767b148af7bff6f58084cf819e21", "ref_doc_id": "06ff538f-b88e-4dd4-823e-715c87189871"}, "c5dd3f07-2306-4d37-9fa5-7042ef53a26d": {"doc_hash": "23d50bb718668a1524175a8cf3c4877b40973bdc14c7e8d5fc69a68dbacd0bcc", "ref_doc_id": "4502b03d-3484-4ac3-82a8-023d5f2f049d"}, "f4459e92-2258-4656-9317-6531a812fe22": {"doc_hash": "61a5dd6d1af302efa9baf3d73bd59b03b3d8c7e46b1a459dac9c45c0cec35c14", "ref_doc_id": "16c055c8-ff6c-4de0-bab6-63170fd073e8"}, "5085708a-5ba6-4282-bda8-1ef4dc697f75": {"doc_hash": "e72b3416b2b4cbdc34f4eff29400b840354c6432e68e1ab5628a936162134ae4", "ref_doc_id": "038c977a-fa74-48c3-8e9b-544ac81a6491"}, "c094ba1b-2a08-4722-a9d4-cf9fa940db82": {"doc_hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "ref_doc_id": "a773b1cc-0970-4eac-b07d-902a0af28368"}, "67e4460d-cf76-456a-837d-b4c9a41d20be": {"doc_hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "ref_doc_id": "a773b1cc-0970-4eac-b07d-902a0af28368"}, "07c89200-70ff-46be-a98f-cec75ca18a9a": {"doc_hash": "d4801c58192f82a245e88de1a19bb28a33f0a9e18d18d6f1c03a4a66ed16a6af", "ref_doc_id": "a773b1cc-0970-4eac-b07d-902a0af28368"}, "d8429f40-d11d-4327-92c3-28a4c54af80f": {"doc_hash": "c7956b55c72ccae868aad8f686c07357f74f7e3cb98c1ee1dfe8f79eded48152", "ref_doc_id": "f6cc6728-01b2-4874-8ab8-168515ceb3c6"}, "267d7498-0059-481d-af9d-c030f7df9593": {"doc_hash": "45bb30ed1c0bbed4d6566af614d1e9f499d20214cb76d94276b3b9bc63f2a130", "ref_doc_id": "34a3addc-3534-41ad-98fc-45181b95e65d"}, "9c94979b-19fb-4485-815b-005784ba418e": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "308e24bd-f4ca-4081-99a5-48db6f74061b"}, "c00fd8df-7b03-4155-a44f-c33bd5b70458": {"doc_hash": "635445276d5e19249546f1424b44bf9fa5e640783d298eab0f5247b9e8bf77f3", "ref_doc_id": "1bff341c-93b4-4ff7-a111-85510f8e6d0f"}, "28208a71-2b0e-434e-ba69-5cc501eee709": {"doc_hash": "19c74261abd32e7209b0ecb48bde680c6dc8c6f8d03ab9f2b4187131aa26542c", "ref_doc_id": "6f7a3f75-888f-48cf-b4b5-7b49d9ee5c11"}, "c64b1ffc-8a2d-44dd-a246-62253f22ed21": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "07027a88-1f43-4556-a861-06d2cf2ab78f"}, "d91d15a2-87d2-4886-b69f-630bca9029fb": {"doc_hash": "abec525e1f8054ab1b6f3df3ec1f2985fadb597968deb19e43afcc3b62935863", "ref_doc_id": "30668e16-a179-400f-84fa-d888c38aab79"}, "6df4f5d3-8d2d-4761-85af-86f3c2b3c026": {"doc_hash": "91a27c3fe45cf1b50a38ad54750809580c10c7eef5af32b7bca0ec0cf5102bc5", "ref_doc_id": "45f27d6e-0f6b-4b17-8e10-020dba0adcdd"}, "fbc903f4-33c7-4533-8e16-4ba056bebe67": {"doc_hash": "c9ad8b8349eb65943fd84666423adb66f5341d023e545a4b6fdae1ceb45d28cb", "ref_doc_id": "2265b92f-320e-411b-a56b-254e313ee1b4"}, "d50c98ad-b329-402b-98ac-2c560d245ae0": {"doc_hash": "8861be49b0d32ecd06406bbe7402a4a0afc00fd5b215cfcd6d7b9d54f7f35a03", "ref_doc_id": "511dc8d6-50c3-4721-ae8d-ae9de0a5ed48"}, "22ddb5a0-d516-4f93-aaed-c6aab32f159f": {"doc_hash": "b400c00b67e57b109365bd64f46e6e13ba54814f38360696b11477ea4f5456cf", "ref_doc_id": "d13c098d-0a08-403e-b739-22e3c89df0df"}, "513ede81-d78e-4d09-9b83-e7ab0c0ea9c4": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "92705802-9679-4d99-9989-4555a37bee5f"}, "dc132540-b69c-4466-bd8c-2a97d75a1e7e": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "1cbf6cd9-c5ba-4535-ae96-d9ac17bb37a8"}, "0905e4b7-0252-4fae-97c9-0cf8543dcff1": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "d0621599-d307-4ae9-8747-5a521c4efe8d"}, "1dd78442-b2c1-4edd-9fcf-0bc86d46984e": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "1512a218-0024-4a6d-a1ae-04e6b82dc116"}, "ad7c657a-0480-44c7-bad0-606bafd7318e": {"doc_hash": "02209da8691416a3d7d77a59fc3caea5741b8db37d9e53370217d3272ea3355f", "ref_doc_id": "edbe15b6-2f55-4fb8-a3bb-a227be57cfd0"}, "a16381eb-c2c0-4d63-a26c-a2676ad12c49": {"doc_hash": "a8986d8d95970402beb17d2ff2c413949926923508ae05cc26a00badb9dedd10", "ref_doc_id": "8c12996a-909c-46d9-a107-a918cdb59b37"}, "77d9ec14-716b-43e4-a807-ba2040f56c73": {"doc_hash": "4719480756e940e66db92a88db9ef11b6c3ea215c887ed12d3df7c4916967b97", "ref_doc_id": "b92600d8-81a7-473f-b79a-4361005b6ac4"}, "e18a4f43-93c3-4537-9858-924c6e4069b6": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "ed5e8e84-863f-4798-b331-70b75e17c1b5"}}, "docstore/ref_doc_info": {"7f893863-704b-4453-bc49-26d48832a498": {"node_ids": ["51c0cfa8-1ce5-4a5a-b1c9-b6726e5f8e6a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "bd0e5fd4-8ae0-4fbc-a521-51198f5363d4": {"node_ids": ["157c4728-368a-4b8d-955c-a4442e908ee3"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "1d76eb40-8aa5-4a4e-b8ac-6a8fc1ef98d0": {"node_ids": ["0f75fd8f-49ea-45f7-baa8-c5f69c685bb4"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "f398f4a1-3058-449e-ba2c-95e6f2ad0c9f": {"node_ids": ["1d0b24b3-7347-4547-a32e-92d5c44e7f8c"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "e452d632-f45d-478e-95c3-d10f9d5ce142": {"node_ids": ["e812bc57-40d2-4406-987f-5f076f91a378"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "8174075a-88ad-4260-bc42-71aa3794c1af": {"node_ids": ["90b5fc87-aa57-4c3d-91a0-09e5a9673bb8"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "b683e9d5-54b7-4f44-8958-c9055d957fb0": {"node_ids": ["fcac93fb-74a2-43ef-b003-cd6dec616469"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}}, "530750fb-63cd-4ab1-a60f-99bfaaca6b9d": {"node_ids": ["4ac3261b-1736-480c-8dad-19e12ea152ef"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "5b8a8c91-7297-4b37-b2a3-117914f12d6c": {"node_ids": ["7e07ca04-3897-4eb0-a7c8-5c62269c3874"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "a00ea1ca-2bc9-4940-89c6-e5d57caee37d": {"node_ids": ["4e23fc6e-1c76-4d3b-8127-8507b0be512c"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}}, "95687766-130d-47d4-a8ed-f12599585a27": {"node_ids": ["0bd95c47-4df0-4c17-b0d6-4e654b8b4701"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}}, "d5b0cb0f-364f-4970-ba74-b9fdf7a77022": {"node_ids": ["38de33d5-0393-4ab6-aaeb-f4f2f00dde69", "341bbfa6-83fa-49ad-9d55-a1b7bad877bc"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}}, "06ff538f-b88e-4dd4-823e-715c87189871": {"node_ids": ["86ff191a-3e29-48dc-9175-c939c5ceea1d"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "4502b03d-3484-4ac3-82a8-023d5f2f049d": {"node_ids": ["c5dd3f07-2306-4d37-9fa5-7042ef53a26d"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "16c055c8-ff6c-4de0-bab6-63170fd073e8": {"node_ids": ["f4459e92-2258-4656-9317-6531a812fe22"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}}, "038c977a-fa74-48c3-8e9b-544ac81a6491": {"node_ids": ["5085708a-5ba6-4282-bda8-1ef4dc697f75"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}}, "a773b1cc-0970-4eac-b07d-902a0af28368": {"node_ids": ["c094ba1b-2a08-4722-a9d4-cf9fa940db82", "67e4460d-cf76-456a-837d-b4c9a41d20be", "07c89200-70ff-46be-a98f-cec75ca18a9a"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}}, "f6cc6728-01b2-4874-8ab8-168515ceb3c6": {"node_ids": ["d8429f40-d11d-4327-92c3-28a4c54af80f"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "34a3addc-3534-41ad-98fc-45181b95e65d": {"node_ids": ["267d7498-0059-481d-af9d-c030f7df9593"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "308e24bd-f4ca-4081-99a5-48db6f74061b": {"node_ids": ["9c94979b-19fb-4485-815b-005784ba418e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "1bff341c-93b4-4ff7-a111-85510f8e6d0f": {"node_ids": ["c00fd8df-7b03-4155-a44f-c33bd5b70458"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}}, "6f7a3f75-888f-48cf-b4b5-7b49d9ee5c11": {"node_ids": ["28208a71-2b0e-434e-ba69-5cc501eee709"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}}, "07027a88-1f43-4556-a861-06d2cf2ab78f": {"node_ids": ["c64b1ffc-8a2d-44dd-a246-62253f22ed21"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "30668e16-a179-400f-84fa-d888c38aab79": {"node_ids": ["d91d15a2-87d2-4886-b69f-630bca9029fb"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}}, "45f27d6e-0f6b-4b17-8e10-020dba0adcdd": {"node_ids": ["6df4f5d3-8d2d-4761-85af-86f3c2b3c026"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}}, "2265b92f-320e-411b-a56b-254e313ee1b4": {"node_ids": ["fbc903f4-33c7-4533-8e16-4ba056bebe67"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "511dc8d6-50c3-4721-ae8d-ae9de0a5ed48": {"node_ids": ["d50c98ad-b329-402b-98ac-2c560d245ae0"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}}, "d13c098d-0a08-403e-b739-22e3c89df0df": {"node_ids": ["22ddb5a0-d516-4f93-aaed-c6aab32f159f"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "92705802-9679-4d99-9989-4555a37bee5f": {"node_ids": ["513ede81-d78e-4d09-9b83-e7ab0c0ea9c4"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "1cbf6cd9-c5ba-4535-ae96-d9ac17bb37a8": {"node_ids": ["dc132540-b69c-4466-bd8c-2a97d75a1e7e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "d0621599-d307-4ae9-8747-5a521c4efe8d": {"node_ids": ["0905e4b7-0252-4fae-97c9-0cf8543dcff1"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "1512a218-0024-4a6d-a1ae-04e6b82dc116": {"node_ids": ["1dd78442-b2c1-4edd-9fcf-0bc86d46984e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "edbe15b6-2f55-4fb8-a3bb-a227be57cfd0": {"node_ids": ["ad7c657a-0480-44c7-bad0-606bafd7318e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "8c12996a-909c-46d9-a107-a918cdb59b37": {"node_ids": ["a16381eb-c2c0-4d63-a26c-a2676ad12c49"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}}, "b92600d8-81a7-473f-b79a-4361005b6ac4": {"node_ids": ["77d9ec14-716b-43e4-a807-ba2040f56c73"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "ed5e8e84-863f-4798-b331-70b75e17c1b5": {"node_ids": ["e18a4f43-93c3-4537-9858-924c6e4069b6"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"51c0cfa8-1ce5-4a5a-b1c9-b6726e5f8e6a": {"__data__": {"id_": "51c0cfa8-1ce5-4a5a-b1c9-b6726e5f8e6a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7f893863-704b-4453-bc49-26d48832a498", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "157c4728-368a-4b8d-955c-a4442e908ee3": {"__data__": {"id_": "157c4728-368a-4b8d-955c-a4442e908ee3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "bd0e5fd4-8ae0-4fbc-a521-51198f5363d4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "7c7241637b1d7da91e846ef2128df0b632edd4db3203d5d7dfef8d7d0d23e708", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:  \n  \n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 599, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0f75fd8f-49ea-45f7-baa8-c5f69c685bb4": {"__data__": {"id_": "0f75fd8f-49ea-45f7-baa8-c5f69c685bb4", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1d76eb40-8aa5-4a4e-b8ac-6a8fc1ef98d0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1d0b24b3-7347-4547-a32e-92d5c44e7f8c": {"__data__": {"id_": "1d0b24b3-7347-4547-a32e-92d5c44e7f8c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f398f4a1-3058-449e-ba2c-95e6f2ad0c9f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "0793cfd15db579cb4e293e94f2bc8560749057301bd65ac3e7e32502a4da3921", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:\n\n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 620, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e812bc57-40d2-4406-987f-5f076f91a378": {"__data__": {"id_": "e812bc57-40d2-4406-987f-5f076f91a378", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e452d632-f45d-478e-95c3-d10f9d5ce142", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "90b5fc87-aa57-4c3d-91a0-09e5a9673bb8": {"__data__": {"id_": "90b5fc87-aa57-4c3d-91a0-09e5a9673bb8", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8174075a-88ad-4260-bc42-71aa3794c1af", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fcac93fb-74a2-43ef-b003-cd6dec616469": {"__data__": {"id_": "fcac93fb-74a2-43ef-b003-cd6dec616469", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b683e9d5-54b7-4f44-8958-c9055d957fb0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4ac3261b-1736-480c-8dad-19e12ea152ef": {"__data__": {"id_": "4ac3261b-1736-480c-8dad-19e12ea152ef", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "530750fb-63cd-4ab1-a60f-99bfaaca6b9d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "fdce9221b7a51d45cd8a679d5ccc41df9957c16c11e87d3652b998375d975c24", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7e07ca04-3897-4eb0-a7c8-5c62269c3874": {"__data__": {"id_": "7e07ca04-3897-4eb0-a7c8-5c62269c3874", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "5b8a8c91-7297-4b37-b2a3-117914f12d6c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "49209c9694101b201ed22f1ed8ed62e41d7cf1e173b99f54510ced22599d19f8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 320 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4e23fc6e-1c76-4d3b-8127-8507b0be512c": {"__data__": {"id_": "4e23fc6e-1c76-4d3b-8127-8507b0be512c", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a00ea1ca-2bc9-4940-89c6-e5d57caee37d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "hash": "8b73ff8299f36ef7a0f1b255fa61f42525982338ba9d3462bc509c1be6a005c3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_3.png\nFile Type: image/png\nSize: 53841 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0bd95c47-4df0-4c17-b0d6-4e654b8b4701": {"__data__": {"id_": "0bd95c47-4df0-4c17-b0d6-4e654b8b4701", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "95687766-130d-47d4-a8ed-f12599585a27", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "hash": "a6165c4eabf5010a45884ca99b9996d2398d5b279ae470d7719b86a9ad1fe54d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_4.png\nFile Type: image/png\nSize: 1615879 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 114, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "38de33d5-0393-4ab6-aaeb-f4f2f00dde69": {"__data__": {"id_": "38de33d5-0393-4ab6-aaeb-f4f2f00dde69", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d5b0cb0f-364f-4970-ba74-b9fdf7a77022", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "341bbfa6-83fa-49ad-9d55-a1b7bad877bc", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "341bbfa6-83fa-49ad-9d55-a1b7bad877bc": {"__data__": {"id_": "341bbfa6-83fa-49ad-9d55-a1b7bad877bc", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d5b0cb0f-364f-4970-ba74-b9fdf7a77022", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "38de33d5-0393-4ab6-aaeb-f4f2f00dde69", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "86ff191a-3e29-48dc-9175-c939c5ceea1d": {"__data__": {"id_": "86ff191a-3e29-48dc-9175-c939c5ceea1d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "06ff538f-b88e-4dd4-823e-715c87189871", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "83e2d625d4eff94277d4c5973581dfd954d6fe91c22d4b6b62e9d280732fdcd9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c5dd3f07-2306-4d37-9fa5-7042ef53a26d": {"__data__": {"id_": "c5dd3f07-2306-4d37-9fa5-7042ef53a26d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4502b03d-3484-4ac3-82a8-023d5f2f049d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "457ca68bef1ae04b512aa529cafe4e5f6f6e8df48959d2decbada8591bbbbf40", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 320 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f4459e92-2258-4656-9317-6531a812fe22": {"__data__": {"id_": "f4459e92-2258-4656-9317-6531a812fe22", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "16c055c8-ff6c-4de0-bab6-63170fd073e8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "hash": "962cbba905397a791d2edc7f5500aa8c000853ce35b1064dacfef269aacad541", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_3.png\nFile Type: image/png\nSize: 176549 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5085708a-5ba6-4282-bda8-1ef4dc697f75": {"__data__": {"id_": "5085708a-5ba6-4282-bda8-1ef4dc697f75", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "038c977a-fa74-48c3-8e9b-544ac81a6491", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "hash": "533092f9d41aeaee314e7236ebd35074574d2d8911229b33ac16516cb3eb8174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_4.png\nFile Type: image/png\nSize: 213826 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c094ba1b-2a08-4722-a9d4-cf9fa940db82": {"__data__": {"id_": "c094ba1b-2a08-4722-a9d4-cf9fa940db82", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a773b1cc-0970-4eac-b07d-902a0af28368", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "67e4460d-cf76-456a-837d-b4c9a41d20be", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "67e4460d-cf76-456a-837d-b4c9a41d20be": {"__data__": {"id_": "67e4460d-cf76-456a-837d-b4c9a41d20be", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a773b1cc-0970-4eac-b07d-902a0af28368", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "c094ba1b-2a08-4722-a9d4-cf9fa940db82", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "07c89200-70ff-46be-a98f-cec75ca18a9a", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "07c89200-70ff-46be-a98f-cec75ca18a9a": {"__data__": {"id_": "07c89200-70ff-46be-a98f-cec75ca18a9a", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "a773b1cc-0970-4eac-b07d-902a0af28368", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "67e4460d-cf76-456a-837d-b4c9a41d20be", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d8429f40-d11d-4327-92c3-28a4c54af80f": {"__data__": {"id_": "d8429f40-d11d-4327-92c3-28a4c54af80f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "f6cc6728-01b2-4874-8ab8-168515ceb3c6", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "c2428cc42d96e259987a8f3b01a481a39b9366ea5875414b8a8efb7c7c4c4505", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "267d7498-0059-481d-af9d-c030f7df9593": {"__data__": {"id_": "267d7498-0059-481d-af9d-c030f7df9593", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "34a3addc-3534-41ad-98fc-45181b95e65d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "0d969717dafc28c81d5ab77bb32c364b168179189b8c3db4dc04e2548b9b1e56", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 11165 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9c94979b-19fb-4485-815b-005784ba418e": {"__data__": {"id_": "9c94979b-19fb-4485-815b-005784ba418e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "308e24bd-f4ca-4081-99a5-48db6f74061b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c00fd8df-7b03-4155-a44f-c33bd5b70458": {"__data__": {"id_": "c00fd8df-7b03-4155-a44f-c33bd5b70458", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1bff341c-93b4-4ff7-a111-85510f8e6d0f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}, "hash": "c55f0a5aab0689358f5954f6fa536cdd563c46835de350432317d86fe7b33dba", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: Manual_GP-PWM-10-FM.pdf\nFile Type: application/pdf\nSize: 2054139 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 119, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "28208a71-2b0e-434e-ba69-5cc501eee709": {"__data__": {"id_": "28208a71-2b0e-434e-ba69-5cc501eee709", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6f7a3f75-888f-48cf-b4b5-7b49d9ee5c11", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle\n\nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1196, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c64b1ffc-8a2d-44dd-a246-62253f22ed21": {"__data__": {"id_": "c64b1ffc-8a2d-44dd-a246-62253f22ed21", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "07027a88-1f43-4556-a861-06d2cf2ab78f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d91d15a2-87d2-4886-b69f-630bca9029fb": {"__data__": {"id_": "d91d15a2-87d2-4886-b69f-630bca9029fb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "30668e16-a179-400f-84fa-d888c38aab79", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}, "hash": "03e8bf0aecb64f104f16037b055fd01bdda109d8b8c8ba456c7e5cccb2f4562b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 125, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6df4f5d3-8d2d-4761-85af-86f3c2b3c026": {"__data__": {"id_": "6df4f5d3-8d2d-4761-85af-86f3c2b3c026", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "45f27d6e-0f6b-4b17-8e10-020dba0adcdd", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "fbc903f4-33c7-4533-8e16-4ba056bebe67": {"__data__": {"id_": "fbc903f4-33c7-4533-8e16-4ba056bebe67", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2265b92f-320e-411b-a56b-254e313ee1b4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "56134e9235b004e89f0701cabd2e3b6d714866b8714a33aa44049a3eb530a11d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 625662 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d50c98ad-b329-402b-98ac-2c560d245ae0": {"__data__": {"id_": "d50c98ad-b329-402b-98ac-2c560d245ae0", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "511dc8d6-50c3-4721-ae8d-ae9de0a5ed48", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "22ddb5a0-d516-4f93-aaed-c6aab32f159f": {"__data__": {"id_": "22ddb5a0-d516-4f93-aaed-c6aab32f159f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d13c098d-0a08-403e-b739-22e3c89df0df", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "6a4c596cd168836f508a796aebf90eaa3a7a4d2f97d09f07b1d0b2e2733c0b5a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 94007 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "513ede81-d78e-4d09-9b83-e7ab0c0ea9c4": {"__data__": {"id_": "513ede81-d78e-4d09-9b83-e7ab0c0ea9c4", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "92705802-9679-4d99-9989-4555a37bee5f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dc132540-b69c-4466-bd8c-2a97d75a1e7e": {"__data__": {"id_": "dc132540-b69c-4466-bd8c-2a97d75a1e7e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1cbf6cd9-c5ba-4535-ae96-d9ac17bb37a8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "0905e4b7-0252-4fae-97c9-0cf8543dcff1": {"__data__": {"id_": "0905e4b7-0252-4fae-97c9-0cf8543dcff1", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "d0621599-d307-4ae9-8747-5a521c4efe8d", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1dd78442-b2c1-4edd-9fcf-0bc86d46984e": {"__data__": {"id_": "1dd78442-b2c1-4edd-9fcf-0bc86d46984e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1512a218-0024-4a6d-a1ae-04e6b82dc116", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ad7c657a-0480-44c7-bad0-606bafd7318e": {"__data__": {"id_": "ad7c657a-0480-44c7-bad0-606bafd7318e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "edbe15b6-2f55-4fb8-a3bb-a227be57cfd0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a16381eb-c2c0-4d63-a26c-a2676ad12c49": {"__data__": {"id_": "a16381eb-c2c0-4d63-a26c-a2676ad12c49", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "8c12996a-909c-46d9-a107-a918cdb59b37", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "77d9ec14-716b-43e4-a807-ba2040f56c73": {"__data__": {"id_": "77d9ec14-716b-43e4-a807-ba2040f56c73", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "b92600d8-81a7-473f-b79a-4361005b6ac4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "8c3809290019e7bbe83bdc51237895f769ee73bd21a42b0f346c7158c95f0a52", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 266051 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e18a4f43-93c3-4537-9858-924c6e4069b6": {"__data__": {"id_": "e18a4f43-93c3-4537-9858-924c6e4069b6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ed5e8e84-863f-4798-b331-70b75e17c1b5", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}