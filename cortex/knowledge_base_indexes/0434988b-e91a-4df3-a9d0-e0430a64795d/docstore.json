{"docstore/metadata": {"e8f79b4d-c75d-4874-9053-79e9fe62f0ad": {"doc_hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd"}, "6eefaece-e9e8-47ea-9580-d4447e06c093": {"doc_hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc"}, "9229abf8-851c-4ec6-b0aa-34e624156160": {"doc_hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997"}, "e63da8c8-9cd9-46b7-b83b-6dfd86aec5e3": {"doc_hash": "f314b26cdbdd8b6fde4ba3e5a4f13c7501ad523fef5c6dc79c53d7a3b6295c9d"}, "fa110af8-7bfc-409f-81c8-cfc710499543": {"doc_hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660"}, "ba3b171f-2963-446a-9675-b234b2b0e712": {"doc_hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548"}, "51f30179-c884-4a70-8680-695485685656": {"doc_hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a"}, "c53beb88-9071-48e6-8f7b-b1706df5eb91": {"doc_hash": "fdce9221b7a51d45cd8a679d5ccc41df9957c16c11e87d3652b998375d975c24"}, "69bed0b6-b3e6-4fbb-835f-3278a0837e39": {"doc_hash": "49209c9694101b201ed22f1ed8ed62e41d7cf1e173b99f54510ced22599d19f8"}, "50b58775-3bb0-49ef-ac8a-e4c09e4ffac3": {"doc_hash": "8b73ff8299f36ef7a0f1b255fa61f42525982338ba9d3462bc509c1be6a005c3"}, "24c8feaa-95e5-4c0f-841a-c59e0d82213b": {"doc_hash": "a6165c4eabf5010a45884ca99b9996d2398d5b279ae470d7719b86a9ad1fe54d"}, "4758a3af-4b17-4026-a968-e613e796fe85": {"doc_hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f"}, "4bf06879-eb9a-4db1-8049-eccdcbdfeecf": {"doc_hash": "83e2d625d4eff94277d4c5973581dfd954d6fe91c22d4b6b62e9d280732fdcd9"}, "6edf1f8c-6a32-4348-98b3-adb833ffc4e4": {"doc_hash": "457ca68bef1ae04b512aa529cafe4e5f6f6e8df48959d2decbada8591bbbbf40"}, "e7db4012-079b-48e2-b471-e31b94cf4c69": {"doc_hash": "962cbba905397a791d2edc7f5500aa8c000853ce35b1064dacfef269aacad541"}, "aa857a3d-d843-4f9d-a082-2e956c90645c": {"doc_hash": "533092f9d41aeaee314e7236ebd35074574d2d8911229b33ac16516cb3eb8174"}, "78c6ccc2-3704-40dc-920e-18c19b076bfc": {"doc_hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d"}, "e07edf7d-512f-4ad0-8020-30982805ac5b": {"doc_hash": "c2428cc42d96e259987a8f3b01a481a39b9366ea5875414b8a8efb7c7c4c4505"}, "778317cb-a447-4fc7-a35d-60ba8638e548": {"doc_hash": "0d969717dafc28c81d5ab77bb32c364b168179189b8c3db4dc04e2548b9b1e56"}, "97baf140-4e5c-42c1-a71b-86fba52ded09": {"doc_hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4"}, "1b56e3bc-e7e6-4436-bf3b-1749708dabcf": {"doc_hash": "c55f0a5aab0689358f5954f6fa536cdd563c46835de350432317d86fe7b33dba"}, "3de4b9aa-f23b-474b-ab2d-57e8c0920565": {"doc_hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae"}, "2796a826-27b7-4762-b530-f761a164e052": {"doc_hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9"}, "ce71d0ff-c4bc-4af7-8317-08425726081e": {"doc_hash": "03e8bf0aecb64f104f16037b055fd01bdda109d8b8c8ba456c7e5cccb2f4562b"}, "1f386d6f-11c7-4139-b10b-7e3d61302b41": {"doc_hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b"}, "7e365c1f-5fa0-4db5-88e7-a7aefe8d0a07": {"doc_hash": "56134e9235b004e89f0701cabd2e3b6d714866b8714a33aa44049a3eb530a11d"}, "25c54b4a-dbd3-497c-9ccb-58cb720c11c4": {"doc_hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b"}, "be43990d-6569-4169-98bf-93cc546e68ab": {"doc_hash": "6a4c596cd168836f508a796aebf90eaa3a7a4d2f97d09f07b1d0b2e2733c0b5a"}, "e658fddf-c1ed-4336-b101-f288c6478978": {"doc_hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d"}, "6c85089b-2dfe-446e-9e4a-66f00b27ad42": {"doc_hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea"}, "468ad797-6e33-4284-b331-642f1512fc0f": {"doc_hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174"}, "0f07e832-e230-49a2-b70d-de494f138f00": {"doc_hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff"}, "1355c3ae-7391-4780-bd02-4f8dcc50fee0": {"doc_hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2"}, "ffb1f091-a2f1-4ac1-8e7c-7de27b597cc0": {"doc_hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98"}, "6cbeebeb-d575-4246-a7ff-d732e0a3e897": {"doc_hash": "8c3809290019e7bbe83bdc51237895f769ee73bd21a42b0f346c7158c95f0a52"}, "046668cc-1391-49b2-8ad2-11364735dbf8": {"doc_hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173"}, "e2c217d0-5de9-4037-9987-4f5e3cc0b956": {"doc_hash": "46a86abed7a8aa7e65c89298e6a89c76dff351da675d4390ab989113dfee86cf", "ref_doc_id": "e8f79b4d-c75d-4874-9053-79e9fe62f0ad"}, "9a4b6d78-945a-4c9b-a448-20017d84345f": {"doc_hash": "e507945e2b39e3d6090cdfaad4a09752efad2de4e59be21ace9ef2e9a7aa4eec", "ref_doc_id": "6eefaece-e9e8-47ea-9580-d4447e06c093"}, "ca07d7ee-6f70-4ffe-bbc1-be1e499ae98f": {"doc_hash": "e8076cdbf063a169a85606ab841d0b0dbe7f0ae8d5923f8d17cbadc695115ebc", "ref_doc_id": "9229abf8-851c-4ec6-b0aa-34e624156160"}, "8b2307dc-0a42-4e6f-a341-aac14fefb9f3": {"doc_hash": "1bc69dea13a28db527ca6d3306d1cca5626aef66483c1310c84c724b98d05b59", "ref_doc_id": "e63da8c8-9cd9-46b7-b83b-6dfd86aec5e3"}, "39a3c54c-01d1-4ef3-8a02-ae9353096f36": {"doc_hash": "580d2a1b32e339c6fe97da39b5b350b3d221e4e7d99946f350d866a1c8c6004a", "ref_doc_id": "fa110af8-7bfc-409f-81c8-cfc710499543"}, "8fa18efb-db5f-4d65-bd0b-62e287512178": {"doc_hash": "dbdd725a2dc3eb9927e4dec367fa659933c9d465f94e5acd6e761f2c6253cc0a", "ref_doc_id": "ba3b171f-2963-446a-9675-b234b2b0e712"}, "8b61bb33-0a47-4628-86b1-969fefe5f027": {"doc_hash": "5f68e1b4f5c3568fdf5c92b7b5568a53fe10ad316ef1a1b4467f212b2dda586e", "ref_doc_id": "51f30179-c884-4a70-8680-695485685656"}, "63e63f9a-9a78-42a0-b98f-0b3f84a29840": {"doc_hash": "7aec3c06a236e4d21e9fc3a44843ff7a881c3b257f306f118246d882311c6cfe", "ref_doc_id": "c53beb88-9071-48e6-8f7b-b1706df5eb91"}, "63e1d53e-ea20-4061-858a-d515b596a5d6": {"doc_hash": "a1989e20f2764b0b87ea8a3fda891195da70f79ccccd8bbd41325d3340e650bc", "ref_doc_id": "69bed0b6-b3e6-4fbb-835f-3278a0837e39"}, "5a9a1f70-ec4f-4593-876d-49b33c0136b7": {"doc_hash": "5f4dd2abdefd7fc5061981fe7bbb37cd617b6ae4eba960ab250309b50523619b", "ref_doc_id": "50b58775-3bb0-49ef-ac8a-e4c09e4ffac3"}, "67150d3a-2f5f-4718-836c-2618e63d7f89": {"doc_hash": "6c15bc58820520458425a2b57388161a18d653a4a6c6f3e9d80e9640c428fe77", "ref_doc_id": "24c8feaa-95e5-4c0f-841a-c59e0d82213b"}, "6794c271-3261-4a82-bdca-45acb5c5e556": {"doc_hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "ref_doc_id": "4758a3af-4b17-4026-a968-e613e796fe85"}, "cfb8847b-a749-4e49-8916-8a0f3ecb1cc9": {"doc_hash": "006639b0301245b1a65425b30dd201c43067eea5724110d5bae59e4512840d8f", "ref_doc_id": "4758a3af-4b17-4026-a968-e613e796fe85"}, "d7905068-9466-4594-94b9-4a63f4f5a914": {"doc_hash": "725e36788c105cc53fd829c0bdb3b13a110c767b148af7bff6f58084cf819e21", "ref_doc_id": "4bf06879-eb9a-4db1-8049-eccdcbdfeecf"}, "05de60aa-0976-4887-b4de-0c654ead2061": {"doc_hash": "23d50bb718668a1524175a8cf3c4877b40973bdc14c7e8d5fc69a68dbacd0bcc", "ref_doc_id": "6edf1f8c-6a32-4348-98b3-adb833ffc4e4"}, "ea565bf4-466d-40c3-9537-1aee6af4cca7": {"doc_hash": "61a5dd6d1af302efa9baf3d73bd59b03b3d8c7e46b1a459dac9c45c0cec35c14", "ref_doc_id": "e7db4012-079b-48e2-b471-e31b94cf4c69"}, "24fbb4be-3d9d-477c-8d30-9e47ea7673a3": {"doc_hash": "e72b3416b2b4cbdc34f4eff29400b840354c6432e68e1ab5628a936162134ae4", "ref_doc_id": "aa857a3d-d843-4f9d-a082-2e956c90645c"}, "a4b4f9bc-6170-40ab-927e-209275b9fc2d": {"doc_hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "ref_doc_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc"}, "dd3f8449-4f5c-4a2c-ad64-120a28006cfb": {"doc_hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "ref_doc_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc"}, "694fdc49-bb47-4dbc-a390-e363d3e774ea": {"doc_hash": "d4801c58192f82a245e88de1a19bb28a33f0a9e18d18d6f1c03a4a66ed16a6af", "ref_doc_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc"}, "96450b34-d974-4849-8f1f-82fc03d406ff": {"doc_hash": "c7956b55c72ccae868aad8f686c07357f74f7e3cb98c1ee1dfe8f79eded48152", "ref_doc_id": "e07edf7d-512f-4ad0-8020-30982805ac5b"}, "3ec9f662-b273-42a8-9d14-077238957283": {"doc_hash": "45bb30ed1c0bbed4d6566af614d1e9f499d20214cb76d94276b3b9bc63f2a130", "ref_doc_id": "778317cb-a447-4fc7-a35d-60ba8638e548"}, "a3a395f7-2265-4fc0-936d-39e2988b1396": {"doc_hash": "7ea12b0a0dce3160de2a5546d4087447c0036dda87c7f9e79a90bd0ab368933c", "ref_doc_id": "97baf140-4e5c-42c1-a71b-86fba52ded09"}, "09388422-5046-4bca-8539-6f74abd4227f": {"doc_hash": "635445276d5e19249546f1424b44bf9fa5e640783d298eab0f5247b9e8bf77f3", "ref_doc_id": "1b56e3bc-e7e6-4436-bf3b-1749708dabcf"}, "290dacff-98fa-4abd-a795-7a6f6a8bf955": {"doc_hash": "19c74261abd32e7209b0ecb48bde680c6dc8c6f8d03ab9f2b4187131aa26542c", "ref_doc_id": "3de4b9aa-f23b-474b-ab2d-57e8c0920565"}, "9e90cafb-ecc3-4cc6-91ac-06f50ce5fbd9": {"doc_hash": "c54ef56571301d2ad42e3a8d6700e2c0f3886e35f6ddac066ff10649210fc3fd", "ref_doc_id": "2796a826-27b7-4762-b530-f761a164e052"}, "bb36577a-31b5-43c6-ade2-29441a4b1e2f": {"doc_hash": "abec525e1f8054ab1b6f3df3ec1f2985fadb597968deb19e43afcc3b62935863", "ref_doc_id": "ce71d0ff-c4bc-4af7-8317-08425726081e"}, "ff806c8a-1586-44c5-a184-182cc62d5176": {"doc_hash": "91a27c3fe45cf1b50a38ad54750809580c10c7eef5af32b7bca0ec0cf5102bc5", "ref_doc_id": "1f386d6f-11c7-4139-b10b-7e3d61302b41"}, "ec971ab3-4f9e-472b-b83c-42954dd15877": {"doc_hash": "c9ad8b8349eb65943fd84666423adb66f5341d023e545a4b6fdae1ceb45d28cb", "ref_doc_id": "7e365c1f-5fa0-4db5-88e7-a7aefe8d0a07"}, "929d1cd6-ab84-4b4f-9601-42da5e37416f": {"doc_hash": "8861be49b0d32ecd06406bbe7402a4a0afc00fd5b215cfcd6d7b9d54f7f35a03", "ref_doc_id": "25c54b4a-dbd3-497c-9ccb-58cb720c11c4"}, "5c677dbb-87a9-47ea-aa16-33db540f06e5": {"doc_hash": "b400c00b67e57b109365bd64f46e6e13ba54814f38360696b11477ea4f5456cf", "ref_doc_id": "be43990d-6569-4169-98bf-93cc546e68ab"}, "27a8b675-5ab0-4bb6-909f-c42b49a2c106": {"doc_hash": "95086298c5e83729393e48d3a01de69be652ccb0cce87c2d3a5413b62d44a0c7", "ref_doc_id": "e658fddf-c1ed-4336-b101-f288c6478978"}, "9960f278-b008-4543-b18f-13a90b52f99e": {"doc_hash": "cc07e8ae0ab0d9cdc301204db87ada4184fb6efa7efae198b015e134ea4ecb13", "ref_doc_id": "6c85089b-2dfe-446e-9e4a-66f00b27ad42"}, "2504d8d9-39fc-44d9-852f-241a6237e51e": {"doc_hash": "4c3c29260dddb6bfa73494e12a4ae1ea63dd7c3b12eb1657e52cec74e3c81b66", "ref_doc_id": "468ad797-6e33-4284-b331-642f1512fc0f"}, "4562066a-d60f-48e0-be05-7b7aa2181f1b": {"doc_hash": "a353a7a35addd8b5c7e01934926d0c33b93337401bc2f3fe8f3533f731ea51f5", "ref_doc_id": "0f07e832-e230-49a2-b70d-de494f138f00"}, "aa731d84-3ee4-4eaa-92d9-61e45df1fb48": {"doc_hash": "02209da8691416a3d7d77a59fc3caea5741b8db37d9e53370217d3272ea3355f", "ref_doc_id": "1355c3ae-7391-4780-bd02-4f8dcc50fee0"}, "658596c7-f634-496c-af1d-c15abe790746": {"doc_hash": "a8986d8d95970402beb17d2ff2c413949926923508ae05cc26a00badb9dedd10", "ref_doc_id": "ffb1f091-a2f1-4ac1-8e7c-7de27b597cc0"}, "7b80768e-93a8-413e-ba64-a7e795d9e357": {"doc_hash": "4719480756e940e66db92a88db9ef11b6c3ea215c887ed12d3df7c4916967b97", "ref_doc_id": "6cbeebeb-d575-4246-a7ff-d732e0a3e897"}, "c3d58c2c-5cf4-444c-895e-3b85a0fbe5d6": {"doc_hash": "cc72d71f09827b9aa6fe3bbafb3817c568b27d20f421d95306ed48190c7e874b", "ref_doc_id": "046668cc-1391-49b2-8ad2-11364735dbf8"}}, "docstore/ref_doc_info": {"e8f79b4d-c75d-4874-9053-79e9fe62f0ad": {"node_ids": ["e2c217d0-5de9-4037-9987-4f5e3cc0b956"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}}, "6eefaece-e9e8-47ea-9580-d4447e06c093": {"node_ids": ["9a4b6d78-945a-4c9b-a448-20017d84345f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}}, "9229abf8-851c-4ec6-b0aa-34e624156160": {"node_ids": ["ca07d7ee-6f70-4ffe-bbc1-be1e499ae98f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}}, "e63da8c8-9cd9-46b7-b83b-6dfd86aec5e3": {"node_ids": ["8b2307dc-0a42-4e6f-a341-aac14fefb9f3"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}}, "fa110af8-7bfc-409f-81c8-cfc710499543": {"node_ids": ["39a3c54c-01d1-4ef3-8a02-ae9353096f36"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}}, "ba3b171f-2963-446a-9675-b234b2b0e712": {"node_ids": ["8fa18efb-db5f-4d65-bd0b-62e287512178"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}}, "51f30179-c884-4a70-8680-695485685656": {"node_ids": ["8b61bb33-0a47-4628-86b1-969fefe5f027"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}}, "c53beb88-9071-48e6-8f7b-b1706df5eb91": {"node_ids": ["63e63f9a-9a78-42a0-b98f-0b3f84a29840"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "69bed0b6-b3e6-4fbb-835f-3278a0837e39": {"node_ids": ["63e1d53e-ea20-4061-858a-d515b596a5d6"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "50b58775-3bb0-49ef-ac8a-e4c09e4ffac3": {"node_ids": ["5a9a1f70-ec4f-4593-876d-49b33c0136b7"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}}, "24c8feaa-95e5-4c0f-841a-c59e0d82213b": {"node_ids": ["67150d3a-2f5f-4718-836c-2618e63d7f89"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}}, "4758a3af-4b17-4026-a968-e613e796fe85": {"node_ids": ["6794c271-3261-4a82-bdca-45acb5c5e556", "cfb8847b-a749-4e49-8916-8a0f3ecb1cc9"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}}, "4bf06879-eb9a-4db1-8049-eccdcbdfeecf": {"node_ids": ["d7905068-9466-4594-94b9-4a63f4f5a914"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "6edf1f8c-6a32-4348-98b3-adb833ffc4e4": {"node_ids": ["05de60aa-0976-4887-b4de-0c654ead2061"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "e7db4012-079b-48e2-b471-e31b94cf4c69": {"node_ids": ["ea565bf4-466d-40c3-9537-1aee6af4cca7"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}}, "aa857a3d-d843-4f9d-a082-2e956c90645c": {"node_ids": ["24fbb4be-3d9d-477c-8d30-9e47ea7673a3"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}}, "78c6ccc2-3704-40dc-920e-18c19b076bfc": {"node_ids": ["a4b4f9bc-6170-40ab-927e-209275b9fc2d", "dd3f8449-4f5c-4a2c-ad64-120a28006cfb", "694fdc49-bb47-4dbc-a390-e363d3e774ea"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}}, "e07edf7d-512f-4ad0-8020-30982805ac5b": {"node_ids": ["96450b34-d974-4849-8f1f-82fc03d406ff"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "778317cb-a447-4fc7-a35d-60ba8638e548": {"node_ids": ["3ec9f662-b273-42a8-9d14-077238957283"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}}, "97baf140-4e5c-42c1-a71b-86fba52ded09": {"node_ids": ["a3a395f7-2265-4fc0-936d-39e2988b1396"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}}, "1b56e3bc-e7e6-4436-bf3b-1749708dabcf": {"node_ids": ["09388422-5046-4bca-8539-6f74abd4227f"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}}, "3de4b9aa-f23b-474b-ab2d-57e8c0920565": {"node_ids": ["290dacff-98fa-4abd-a795-7a6f6a8bf955"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}}, "2796a826-27b7-4762-b530-f761a164e052": {"node_ids": ["9e90cafb-ecc3-4cc6-91ac-06f50ce5fbd9"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}}, "ce71d0ff-c4bc-4af7-8317-08425726081e": {"node_ids": ["bb36577a-31b5-43c6-ade2-29441a4b1e2f"], "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}}, "1f386d6f-11c7-4139-b10b-7e3d61302b41": {"node_ids": ["ff806c8a-1586-44c5-a184-182cc62d5176"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}}, "7e365c1f-5fa0-4db5-88e7-a7aefe8d0a07": {"node_ids": ["ec971ab3-4f9e-472b-b83c-42954dd15877"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "25c54b4a-dbd3-497c-9ccb-58cb720c11c4": {"node_ids": ["929d1cd6-ab84-4b4f-9601-42da5e37416f"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}}, "be43990d-6569-4169-98bf-93cc546e68ab": {"node_ids": ["5c677dbb-87a9-47ea-aa16-33db540f06e5"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "e658fddf-c1ed-4336-b101-f288c6478978": {"node_ids": ["27a8b675-5ab0-4bb6-909f-c42b49a2c106"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}}, "6c85089b-2dfe-446e-9e4a-66f00b27ad42": {"node_ids": ["9960f278-b008-4543-b18f-13a90b52f99e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}}, "468ad797-6e33-4284-b331-642f1512fc0f": {"node_ids": ["2504d8d9-39fc-44d9-852f-241a6237e51e"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}}, "0f07e832-e230-49a2-b70d-de494f138f00": {"node_ids": ["4562066a-d60f-48e0-be05-7b7aa2181f1b"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}}, "1355c3ae-7391-4780-bd02-4f8dcc50fee0": {"node_ids": ["aa731d84-3ee4-4eaa-92d9-61e45df1fb48"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}}, "ffb1f091-a2f1-4ac1-8e7c-7de27b597cc0": {"node_ids": ["658596c7-f634-496c-af1d-c15abe790746"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}}, "6cbeebeb-d575-4246-a7ff-d732e0a3e897": {"node_ids": ["7b80768e-93a8-413e-ba64-a7e795d9e357"], "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}}, "046668cc-1391-49b2-8ad2-11364735dbf8": {"node_ids": ["c3d58c2c-5cf4-444c-895e-3b85a0fbe5d6"], "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}}}, "docstore/data": {"e2c217d0-5de9-4037-9987-4f5e3cc0b956": {"__data__": {"id_": "e2c217d0-5de9-4037-9987-4f5e3cc0b956", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e8f79b4d-c75d-4874-9053-79e9fe62f0ad", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Change Your Email Preferences", "url": "https://meghanag.supporthive.com/kb/article/9-how-to-change-your-email-preferences/", "article_id": 9, "section_id": 2, "last_updated_at": "2025-04-18T03:44:32Z", "has_attachments": false, "attachment_count": 0}, "hash": "fed439483c0cc24384562934cb28505168d786fa1acbc5769afd1c0e12835bcd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "To customize your email preferences, follow these steps:\n\n  1. Log in to your account.\n\n  2. Navigate to \"Settings\" in the top-right corner.\n\n  3. Click on \"Email Preferences.\"\n\n  4. Choose which types of emails you’d like to receive (e.g., system alerts, updates, newsletters).\n\n  5. Save your changes.\n\n**Troubleshooting:**\n\n  * If you no longer want to receive certain types of emails, make sure to unsubscribe or adjust your email preferences.\n\n  * If emails are being marked as spam, whitelist our email address in your email client.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 538, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9a4b6d78-945a-4c9b-a448-20017d84345f": {"__data__": {"id_": "9a4b6d78-945a-4c9b-a448-20017d84345f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6eefaece-e9e8-47ea-9580-d4447e06c093", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password", "url": "https://meghanag.supporthive.com/kb/article/6-how-to-reset-your-password/", "article_id": 6, "section_id": 2, "last_updated_at": "2025-04-18T03:41:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "8a5494c441ece48633a53b75daf0b05311652c44f689c4ba9733268764f59dcc", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you’ve forgotten your password or wish to change it for security reasons,\nfollow these steps to reset your password:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password?\" link.\n\n  3. Enter your registered email address.\n\n  4. Check your inbox for a password reset email.\n\n  5. Click on the \"Reset Password\" link in the email.\n\n  6. Enter a new password and confirm it.\n\n  7. Log in with your new password.\n\n**Troubleshooting:**\n\n  * If you do not receive the reset email, check your spam or junk folder.\n\n  * Ensure you’ve entered the correct email address linked to your account.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 595, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ca07d7ee-6f70-4ffe-bbc1-be1e499ae98f": {"__data__": {"id_": "ca07d7ee-6f70-4ffe-bbc1-be1e499ae98f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "9229abf8-851c-4ec6-b0aa-34e624156160", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Reset Your Password in HappyFox", "url": "https://meghanag.supporthive.com/kb/article/11-how-to-reset-your-password-in-happyfox/", "article_id": 11, "section_id": 2, "last_updated_at": "2025-04-18T07:53:43Z", "has_attachments": false, "attachment_count": 0}, "hash": "2a28e58b4932f7c9607679250c65ac717418c470d4199269ada2af86dd087997", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need to reset your password for your HappyFox account, follow these\nsteps:\n\n  1. Go to the login page.\n\n  2. Click on the \"Forgot Password\" link.\n\n  3. Enter your registered email address.\n\n  4. You'll receive an email with a link to reset your password.\n\n  5. Click the link, and you'll be able to set a new password.\n\n**Note:** If you do not receive the email within a few minutes, check your\nspam folder or try again later.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 433, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8b2307dc-0a42-4e6f-a341-aac14fefb9f3": {"__data__": {"id_": "8b2307dc-0a42-4e6f-a341-aac14fefb9f3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e63da8c8-9cd9-46b7-b83b-6dfd86aec5e3", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Submit a Ticket for Support", "url": "https://meghanag.supporthive.com/kb/article/8-how-to-submit-a-ticket-for-support/", "article_id": 8, "section_id": 2, "last_updated_at": "2025-04-18T03:43:50Z", "has_attachments": false, "attachment_count": 0}, "hash": "f314b26cdbdd8b6fde4ba3e5a4f13c7501ad523fef5c6dc79c53d7a3b6295c9d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "If you need further assistance, submitting a support ticket is the best way to\nget help from our team:  \n  \n  1. Log in to your account.\n\n  2. Go to the \"Support\" section.\n\n  3. Click on \"Submit a Ticket.\"\n\n  4. Select a category for your issue.\n\n  5. Fill out the ticket form with all relevant details.\n\n  6. Click \"Submit\" to send your ticket to our support team.\n\n  7. You will receive a confirmation email with a ticket number.\n\n**Troubleshooting:**\n\n  * If you do not see the ticket form, ensure you are logged in to your account.\n\n  * Make sure to provide as much detail as possible to expedite the resolution process.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 624, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "39a3c54c-01d1-4ef3-8a02-ae9353096f36": {"__data__": {"id_": "39a3c54c-01d1-4ef3-8a02-ae9353096f36", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "fa110af8-7bfc-409f-81c8-cfc710499543", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Update Your Contact Information", "url": "https://meghanag.supporthive.com/kb/article/7-how-to-update-your-contact-information/", "article_id": 7, "section_id": 2, "last_updated_at": "2025-04-18T03:42:44Z", "has_attachments": false, "attachment_count": 0}, "hash": "b0b36b00f4924589f7716b0e660006c6f5a576940cfc53283c232b07727b2660", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "You can update your contact information directly from your account settings:\n\n  1. Log in to your account.\n\n  2. Click on your profile picture in the top-right corner.\n\n  3. Select \"Account Settings.\"\n\n  4. Under \"Personal Information,\" click \"Edit.\"\n\n  5. Update your name, email, and phone number as needed.\n\n  6. Save your changes.\n\n**Troubleshooting:**\n\n  * Ensure the email address is valid and correctly formatted.\n\n  * If you experience issues saving changes, clear your browser cache or try a different browser.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 519, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8fa18efb-db5f-4d65-bd0b-62e287512178": {"__data__": {"id_": "8fa18efb-db5f-4d65-bd0b-62e287512178", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ba3b171f-2963-446a-9675-b234b2b0e712", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "How to Use Two-Factor Authentication (2FA)", "url": "https://meghanag.supporthive.com/kb/article/10-how-to-use-two-factor-authentication-2fa/", "article_id": 10, "section_id": 2, "last_updated_at": "2025-04-18T03:45:13Z", "has_attachments": false, "attachment_count": 0}, "hash": "d44a3ea2fc59d7eb8f9fa036a2871a8178290980950207be3df0ad42ab132548", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Two-factor authentication (2FA) adds an extra layer of security to your\naccount. Here’s how to enable it:\n\n  1. Log in to your account.\n\n  2. Go to \"Account Settings.\"\n\n  3. Under \"Security Settings,\" click on \"Enable Two-Factor Authentication.\"\n\n  4. Choose your preferred method (e.g., mobile app, email).\n\n  5. Follow the instructions to link your account to your 2FA method.\n\n  6. Enter the verification code sent to your device to complete the setup.\n\n**Troubleshooting:**\n\n  * If you lose access to your 2FA method, contact support for assistance.\n\n  * Ensure that the device used for 2FA is set up correctly to avoid verification issues.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 644, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8b61bb33-0a47-4628-86b1-969fefe5f027": {"__data__": {"id_": "8b61bb33-0a47-4628-86b1-969fefe5f027", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "51f30179-c884-4a70-8680-695485685656", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Tables as image and images", "url": "https://meghanag.supporthive.com/kb/article/16-kb-1-tables-as-image-and-images/", "article_id": 16, "section_id": 2, "last_updated_at": "2025-04-29T06:01:25Z", "has_attachments": true, "attachment_count": 4}, "hash": "210ad9ca2605aa5359af49ece2a2e89183f9592a31acbc1859cc1afd5e5f586a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Duralite-100E Expansion Kit\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nMonsoon Gohain\n\nSep 01, 2022\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 927\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png)![](https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 592, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "63e63f9a-9a78-42a0-b98f-0b3f84a29840": {"__data__": {"id_": "63e63f9a-9a78-42a0-b98f-0b3f84a29840", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "c53beb88-9071-48e6-8f7b-b1706df5eb91", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "fdce9221b7a51d45cd8a679d5ccc41df9957c16c11e87d3652b998375d975c24", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "63e1d53e-ea20-4061-858a-d515b596a5d6": {"__data__": {"id_": "63e1d53e-ea20-4061-858a-d515b596a5d6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "69bed0b6-b3e6-4fbb-835f-3278a0837e39", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "49209c9694101b201ed22f1ed8ed62e41d7cf1e173b99f54510ced22599d19f8", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 320 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5a9a1f70-ec4f-4593-876d-49b33c0136b7": {"__data__": {"id_": "5a9a1f70-ec4f-4593-876d-49b33c0136b7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "50b58775-3bb0-49ef-ac8a-e4c09e4ffac3", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/37f48d3f-8d70-400d-8ed6-181972e8a8e4/Screen_Shot_2022-07-13_at_12.35.04_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 53841, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "hash": "8b73ff8299f36ef7a0f1b255fa61f42525982338ba9d3462bc509c1be6a005c3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_3.png\nFile Type: image/png\nSize: 53841 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "67150d3a-2f5f-4718-836c-2618e63d7f89": {"__data__": {"id_": "67150d3a-2f5f-4718-836c-2618e63d7f89", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "24c8feaa-95e5-4c0f-841a-c59e0d82213b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 16, "parent_article_title": "KB-1 Tables as image and images", "attachment_id": "img_16_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-\nfiles-\noregon.s3.amazonaws.com/hdpgopower_kb_attachments/2022/07-13/681a6847-eac3-485e-8ef7-3ab20542f58e/Duralite-100E_Expansion_Kit.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 1615879, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_16_image_img_16_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "hash": "a6165c4eabf5010a45884ca99b9996d2398d5b279ae470d7719b86a9ad1fe54d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_4.png\nFile Type: image/png\nSize: 1615879 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 114, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6794c271-3261-4a82-bdca-45acb5c5e556": {"__data__": {"id_": "6794c271-3261-4a82-bdca-45acb5c5e556", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4758a3af-4b17-4026-a968-e613e796fe85", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "cfb8847b-a749-4e49-8916-8a0f3ecb1cc9", "node_type": "1", "metadata": {}, "hash": "f134e096d4bdf5b4db087bb4babc987598a1a8c457e30d2146add173551b2a14", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Automate Okta Identity Lifecycle Actions for requests in Zendesk\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\nNov 05, 2021\n\n![views\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg) 2209\n\nBy using HappyFox Workflows, IT admins can automate everyday identity\nlifecycle actions in Okta for user requests from Zendesk.\n\n**List of Okta actions that can be invoked/automated inside Zendesk:**\n\n  * Password resets (including expiring temporary password)\n  * Account unlocks\n  * Resetting any MFA enrollments\n  * Clearing existing user sessions\n  * Account suspensions\n  * Account deactivations.\n\n_[Click here](https://www.happyfox.com/workflows-for-zendesk-support/#book-\ndemo) _to book a 1-on-1 demo with our product experts\n\n**Pre-requisite**\n\nAn active HappyFox Workflows account with Zendesk support and Okta\nsuccessfully integrated.\n\n_Quicklinks:_\n\n  * [Zendesk configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1002-how-to-configure-happyfox-workflows-for-zendesk-support/)\n  * [Okta configuration with HappyFox Workflows](https://support.happyfox.com/kb/article/1136-configure-okta-integration-with-happyfox-workflows/)\n\n**Automated Workflow Example:**\n\n  1. A User raises an Okta unlock request in Zendesk\n  2. HappyFox Workflows automatically detects an unlock request and validates it.\n  3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n!", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1584, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "cfb8847b-a749-4e49-8916-8a0f3ecb1cc9": {"__data__": {"id_": "cfb8847b-a749-4e49-8916-8a0f3ecb1cc9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4758a3af-4b17-4026-a968-e613e796fe85", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "3d8f0fd8446ed2b6f59eff6c9b4f3847538ba4acd8b12c0c51c06a25d48d2b6f", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "6794c271-3261-4a82-bdca-45acb5c5e556", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Images", "url": "https://meghanag.supporthive.com/kb/article/14-kb-1-text-images/", "article_id": 14, "section_id": 2, "last_updated_at": "2025-04-29T05:57:05Z", "has_attachments": true, "attachment_count": 4}, "hash": "ac85faaeb48acd761412c4d5b7c89c0dac2316a0863ca23ca7f1d8c3cc98774d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "3. HappyFox Workflows initiates the user unlock action in Okta automatically.\n  4. HappyFox Workflows sends an update to the user on the next steps.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png)\n\n**Manually Triggering Okta Actions:**\n\n<PERSON><PERSON> can also set up HappyFox Workflows configuration to manually trigger\nOkta actions from the context of a Zendesk Ticket.\n\n![](https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png)\n\nFeedback\n\n4 out of 4 found this helpful", "mimetype": "text/plain", "start_char_idx": 1433, "end_char_idx": 2113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d7905068-9466-4594-94b9-4a63f4f5a914": {"__data__": {"id_": "d7905068-9466-4594-94b9-4a63f4f5a914", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "4bf06879-eb9a-4db1-8049-eccdcbdfeecf", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "83e2d625d4eff94277d4c5973581dfd954d6fe91c22d4b6b62e9d280732fdcd9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "05de60aa-0976-4887-b4de-0c654ead2061": {"__data__": {"id_": "05de60aa-0976-4887-b4de-0c654ead2061", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6edf1f8c-6a32-4348-98b3-adb833ffc4e4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/view-\ncount.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 320, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "457ca68bef1ae04b512aa529cafe4e5f6f6e8df48959d2decbada8591bbbbf40", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 320 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ea565bf4-466d-40c3-9537-1aee6af4cca7": {"__data__": {"id_": "ea565bf4-466d-40c3-9537-1aee6af4cca7", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e7db4012-079b-48e2-b471-e31b94cf4c69", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_3", "attachment_name": "embedded_image_3.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/ad543b89-39d4-4818-8dc1-b03c65f1d8e5/Screenshot_2021-11-05_at_8.45.10_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 176549, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_3_embedded_image_3.png", "section_id": 2, "is_embedded_image": true}, "hash": "962cbba905397a791d2edc7f5500aa8c000853ce35b1064dacfef269aacad541", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_3.png\nFile Type: image/png\nSize: 176549 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "24fbb4be-3d9d-477c-8d30-9e47ea7673a3": {"__data__": {"id_": "24fbb4be-3d9d-477c-8d30-9e47ea7673a3", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "aa857a3d-d843-4f9d-a082-2e956c90645c", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 14, "parent_article_title": "KB-1 Text & Images", "attachment_id": "img_14_4", "attachment_name": "embedded_image_4.png", "attachment_url": "https://hf-files-\noregon.s3.amazonaws.com/hdpsupport_kb_attachments/2021/11-05/543865ac-3d4f-40fb-a066-134516d6c83d/Screenshot_2021-11-05_at_9.01.02_PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 213826, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_14_image_img_14_4_embedded_image_4.png", "section_id": 2, "is_embedded_image": true}, "hash": "533092f9d41aeaee314e7236ebd35074574d2d8911229b33ac16516cb3eb8174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_4.png\nFile Type: image/png\nSize: 213826 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a4b4f9bc-6170-40ab-927e-209275b9fc2d": {"__data__": {"id_": "a4b4f9bc-6170-40ab-927e-209275b9fc2d", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "dd3f8449-4f5c-4a2c-ad64-120a28006cfb", "node_type": "1", "metadata": {}, "hash": "acfd6453f89c628bc19f6949a31a8aa520a37b48688a8ff900926532cd9b072e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Recorder and RAID Default Login List\n\n![print\nicon](https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg)\n\n![https://hf-files-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png](https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png)\n\n# **Recorder and RAID Default Login List**\n\n\\-----------------------------------\n\n**Affected Roles:** Administrator, Owner\n\n**Related Digital Watchdog VMS Apps:** DW Spectrum® IPVMS\n\n**Last Edit:** August 6, 2024\n\n\\-----------------------------------\n\n# **De<PERSON>ult <PERSON>**\n\nThe DW Blackjack® Series and VMAX® Series units are shipped to use the default\nlogin credentials. DW Blackjack® units will typically not require a login to\naccess the OS, but VMAX® DVRs and NVRs do require the user to enter a login\nupon booting to use the unit.\n\nAdditionally, some DW Blackjack® Servers feature the LSI RAID Manager\nsoftware, which allows Administrators to manage and maintain the RAID array of\nthose special units.\n\nThis article will list the default login credentials of the DW Blackjack® and\nVMAX® recording units, as well as the default login to the LSI RAID Manager\nprogram for DW Blackjack® units with RAID support.\n\n****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1474, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "dd3f8449-4f5c-4a2c-ad64-120a28006cfb": {"__data__": {"id_": "dd3f8449-4f5c-4a2c-ad64-120a28006cfb", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "a4b4f9bc-6170-40ab-927e-209275b9fc2d", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "bdcb4c5794e460093265ac30d66a71a84bcffbe7bc09e339f84a3c42fc960b99", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "694fdc49-bb47-4dbc-a390-e363d3e774ea", "node_type": "1", "metadata": {}, "hash": "953bec790a5244a0c3353b325dae66da5a7e2aea7181eb05fe3829cf6a9ba033", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** The default login for the DW Spectrum® IPVMS software\n(admin/admin12345) is the same on all DW Blackjack® units.\n\n# **Supported/Affected Devices:**\n\n  * DW Blackjack® Cube Series\n  * DW Blackjack® E-Rack/P-Rack Series\n  * DW Blackjack® X-Rack Series\n  * DW Blackjack® Intel Xeon Silver Processor 2U Series\n  * DW Blackjack® Tower Series\n  * DW Blackjack® MINI Series\n  * DW Blackjack® NAS Series\n  * VMAX® A1 Plus™ Series\n  * VMAX® IP Plus™ Series\n\n# **Default DW Blackjack Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nDW Blackjack® Cube |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® E-Rack & P-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Tower & Mid-Tower |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® X-Rack |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® Intel Xeon Silver Processor 2U |  DHCP |  dwuser/Dw5pectrum  \nDW Blackjack® MINI |  DHCP |  admin/admin1234  \nDW Blackjack® NAS |  ************* |  admin/admin1234  \n  \n# ****NOTE:** For DW Blackjack® Servers with Windows, purchased prior to June\n18, 2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.", "mimetype": "text/plain", "start_char_idx": 1350, "end_char_idx": 2643, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "694fdc49-bb47-4dbc-a390-e363d3e774ea": {"__data__": {"id_": "694fdc49-bb47-4dbc-a390-e363d3e774ea", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "78c6ccc2-3704-40dc-920e-18c19b076bfc", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "7534a49773494e782ca6357ae21128bd9b4102502f93741a331d577977fd468d", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "dd3f8449-4f5c-4a2c-ad64-120a28006cfb", "node_type": "1", "metadata": {"source": "HappyFox", "content_type": "article", "title": "KB-1 Text & Table", "url": "https://meghanag.supporthive.com/kb/article/15-kb-1-text-table/", "article_id": 15, "section_id": 2, "last_updated_at": "2025-04-29T05:58:40Z", "has_attachments": true, "attachment_count": 2}, "hash": "dee32cfe73d7c9df921dfa1f0311a51d1b77b68c8716cf64711d6265ad7a2b99", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "****NOTE:** For DW Blackjack® Servers with Linux, purchased prior to June 18,\n2021, the previously utilized default OS login is “ _admin/admin_ ”.\n\n# **Default LSI RAID Manager Login List**\n\n**Device Series** |  **Default RAID Login** |  **Example Login**  \n---|---|---  \nDW Blackjack® P-Rack and E-Rack (Windows) |  <system name>/admin |  _bjer4u120t/admin_  \nDW Blackjack® P-Rack and E-Rack (Ubuntu/Linux) |  root/admin |   \nDW Blackjack® X-Rack |  x-rack/Xrack1234 |   \n  \n# **Default VMAX® Unit Login List**\n\n**Device Series** |  **Default IP Address** |  **Default Unit Login**  \n---|---|---  \nVMAX® A1 Plus and VMAX A1 G4 |  ************* |  admin/<no password>  \nVMAX® IP Plus and VMAX VG4 |  ************* |  admin/<no password>  \nDW Compressor |  ************* |  admin/admin", "mimetype": "text/plain", "start_char_idx": 2497, "end_char_idx": 3281, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "96450b34-d974-4849-8f1f-82fc03d406ff": {"__data__": {"id_": "96450b34-d974-4849-8f1f-82fc03d406ff", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e07edf7d-512f-4ad0-8020-30982805ac5b", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://d12tly1s0ox52d.cloudfront.net/static/************/support_center/svgs/print.svg", "mime_type": "image/png", "file_extension": ".png", "file_size": 430, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "c2428cc42d96e259987a8f3b01a481a39b9366ea5875414b8a8efb7c7c4c4505", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 430 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 110, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3ec9f662-b273-42a8-9d14-077238957283": {"__data__": {"id_": "3ec9f662-b273-42a8-9d14-077238957283", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "778317cb-a447-4fc7-a35d-60ba8638e548", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 15, "parent_article_title": "KB-1 Text & Table", "attachment_id": "img_15_2", "attachment_name": "embedded_image_2.png", "attachment_url": "https://hf-\nfiles-oregon.s3-us-\nwest-2.amazonaws.com/hdpdigitalwatchdog_kb_attachments/2019/09-24/4763eb2b-7c2d-49d6-bfc0-47620cc0f150/image.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 11165, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_15_image_img_15_2_embedded_image_2.png", "section_id": 2, "is_embedded_image": true}, "hash": "0d969717dafc28c81d5ab77bb32c364b168179189b8c3db4dc04e2548b9b1e56", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_2.png\nFile Type: image/png\nSize: 11165 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "a3a395f7-2265-4fc0-936d-39e2988b1396": {"__data__": {"id_": "a3a395f7-2265-4fc0-936d-39e2988b1396", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "97baf140-4e5c-42c1-a71b-86fba52ded09", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "PDF", "url": "https://meghanag.supporthive.com/kb/article/22-pdf/", "article_id": 22, "section_id": 2, "last_updated_at": "2025-06-16T09:01:30Z", "has_attachments": true, "attachment_count": 1}, "hash": "c221f98bf900ef40f44012a543dc67506611ee2b91ecc14f516565cd873755d4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "attached documents test", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 23, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "09388422-5046-4bca-8539-6f74abd4227f": {"__data__": {"id_": "09388422-5046-4bca-8539-6f74abd4227f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1b56e3bc-e7e6-4436-bf3b-1749708dabcf", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 22, "parent_article_title": "PDF", "attachment_id": 2, "attachment_name": "Manual_GP-PWM-10-FM.pdf", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/06-16/682f9daa-230e-4dc0-9ac0-2734edcb02cd/Manual_GP-PWM-10-FM.pdf", "mime_type": "application/pdf", "file_extension": ".pdf", "file_size": 2054139, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_22_attachment_2_Manual_GP-PWM-10-FM.pdf", "section_id": 2, "is_embedded_image": false}, "hash": "c55f0a5aab0689358f5954f6fa536cdd563c46835de350432317d86fe7b33dba", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: Manual_GP-PWM-10-FM.pdf\nFile Type: application/pdf\nSize: 2054139 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 119, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "290dacff-98fa-4abd-a795-7a6f6a8bf955": {"__data__": {"id_": "290dacff-98fa-4abd-a795-7a6f6a8bf955", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "3de4b9aa-f23b-474b-ab2d-57e8c0920565", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample article", "url": "https://meghanag.supporthive.com/kb/article/12-sample-article/", "article_id": 12, "section_id": 2, "last_updated_at": "2025-04-22T05:44:42Z", "has_attachments": false, "attachment_count": 0}, "hash": "f1313277034e8b093b35b3352fa6f57c45a1829a90009999587d67527e82c0ae", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Understanding Your Billing Cycle\n\nYour billing cycle is determined by your signup date and plan:\n\n\\- Monthly plans renew every 30 days.  \n\\- Annual plans renew once every 12 months.  \n\\- You’ll be billed automatically unless auto-renew is turned off.  \n\\- Receipts are emailed after each successful payment.\n\nTo view your billing history, go to Settings > Billing > History.  \n\nHow to Delete Your Account\n\nWarning: Deleting your account is permanent.\n\nSteps to delete your account:\n\n1\\. Log in and go to Settings > Account.  \n2\\. <PERSON><PERSON> down and click on \"Delete My Account\".  \n3\\. Enter your password to confirm the action.  \n4\\. You will receive a confirmation email. Click the link to complete\ndeletion.\n\nYour data will be permanently removed within 7 days. This action is\nirreversible.  \n\nHow to change email address\n\nTo update your registered email address:\n\n1\\. Log in to your account.  \n2\\. Navigate to Settings > Profile.  \n3\\. Click \"Edit\" next to your email address.  \n4\\. Enter the new email and confirm.  \n5\\. A verification email will be sent to the new address.  \n6\\. Click the verification link to complete the update.\n\nNote: You must verify the new address before it takes effect.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1196, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9e90cafb-ecc3-4cc6-91ac-06f50ce5fbd9": {"__data__": {"id_": "9e90cafb-ecc3-4cc6-91ac-06f50ce5fbd9", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "2796a826-27b7-4762-b530-f761a164e052", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample docx", "url": "https://meghanag.supporthive.com/kb/article/17-sample-docx/", "article_id": 17, "section_id": 2, "last_updated_at": "2025-05-12T06:18:44Z", "has_attachments": true, "attachment_count": 1}, "hash": "94daa94105e4847e0d84debabad77a2032e2b63fe25745cd0ec4a35de5556ae9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "test docx", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 9, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bb36577a-31b5-43c6-ade2-29441a4b1e2f": {"__data__": {"id_": "bb36577a-31b5-43c6-ade2-29441a4b1e2f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ce71d0ff-c4bc-4af7-8317-08425726081e", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "attachment", "parent_article_id": 17, "parent_article_title": "sample docx", "attachment_id": 1, "attachment_name": "sample_kb_article.docx", "attachment_url": "https://hf-staging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-12/69860b5a-91bb-4339-bf21-5b9c647d9053/sample_kb_article.docx", "mime_type": "application/octet-stream", "file_extension": ".docx", "file_size": 37001, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_17_attachment_1_sample_kb_article.docx", "section_id": 2, "is_embedded_image": false}, "hash": "03e8bf0aecb64f104f16037b055fd01bdda109d8b8c8ba456c7e5cccb2f4562b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Attachment: sample_kb_article.docx\nFile Type: application/octet-stream\nSize: 37001 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 125, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ff806c8a-1586-44c5-a184-182cc62d5176": {"__data__": {"id_": "ff806c8a-1586-44c5-a184-182cc62d5176", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1f386d6f-11c7-4139-b10b-7e3d61302b41", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "sample image", "url": "https://meghanag.supporthive.com/kb/article/19-sample-image/", "article_id": 19, "section_id": 2, "last_updated_at": "2025-05-14T11:23:38Z", "has_attachments": true, "attachment_count": 1}, "hash": "f75323da85e9730f572c96f760cd00697eaa56b143fbbf748f3b7d2d0f3c474b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ec971ab3-4f9e-472b-b83c-42954dd15877": {"__data__": {"id_": "ec971ab3-4f9e-472b-b83c-42954dd15877", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "7e365c1f-5fa0-4db5-88e7-a7aefe8d0a07", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 19, "parent_article_title": "sample image", "attachment_id": "img_19_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-14/89f0213e-8ddf-415e-8bce-1f32bdbe8c43/Screenshot_2025-05-14_at_4.52.20PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 625662, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_19_image_img_19_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "56134e9235b004e89f0701cabd2e3b6d714866b8714a33aa44049a3eb530a11d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 625662 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "929d1cd6-ab84-4b4f-9601-42da5e37416f": {"__data__": {"id_": "929d1cd6-ab84-4b4f-9601-42da5e37416f", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "25c54b4a-dbd3-497c-9ccb-58cb720c11c4", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "segment", "url": "https://meghanag.supporthive.com/kb/article/20-segment/", "article_id": 20, "section_id": 2, "last_updated_at": "2025-05-15T15:07:55Z", "has_attachments": true, "attachment_count": 1}, "hash": "ed3f62cbe935a18eef58b651a4711eb818d9dcae7596c9f67608233353d93e3b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5c677dbb-87a9-47ea-aa16-33db540f06e5": {"__data__": {"id_": "5c677dbb-87a9-47ea-aa16-33db540f06e5", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "be43990d-6569-4169-98bf-93cc546e68ab", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 20, "parent_article_title": "segment", "attachment_id": "img_20_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-15/9970e608-34ec-4d3e-8230-a0be6f858b31/Screenshot_2025-05-15_at_8.37.11PM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 94007, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_20_image_img_20_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "6a4c596cd168836f508a796aebf90eaa3a7a4d2f97d09f07b1d0b2e2733c0b5a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 94007 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 112, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "27a8b675-5ab0-4bb6-909f-c42b49a2c106": {"__data__": {"id_": "27a8b675-5ab0-4bb6-909f-c42b49a2c106", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "e658fddf-c1ed-4336-b101-f288c6478978", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article", "url": "https://meghanag.supporthive.com/kb/article/1-test-article/", "article_id": 1, "section_id": 2, "last_updated_at": "2025-04-08T16:05:41Z", "has_attachments": false, "attachment_count": 0}, "hash": "ffa5c5d8d8cec1e1f4458b09a64779bd868870ac763e29aaadc2796130dfbd8d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "This is a sample knowledge base article to test the HappyFox API reader.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 72, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "9960f278-b008-4543-b18f-13a90b52f99e": {"__data__": {"id_": "9960f278-b008-4543-b18f-13a90b52f99e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6c85089b-2dfe-446e-9e4a-66f00b27ad42", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 2", "url": "https://meghanag.supporthive.com/kb/article/2-test-article-2/", "article_id": 2, "section_id": 2, "last_updated_at": "2025-04-09T10:10:39Z", "has_attachments": false, "attachment_count": 0}, "hash": "7494f46e4dc66339eca63b3f344828bb960c709fab60fee43a26b072773fb5ea", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "`This is another test article to verify HappyFox reader updates.`", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 65, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2504d8d9-39fc-44d9-852f-241a6237e51e": {"__data__": {"id_": "2504d8d9-39fc-44d9-852f-241a6237e51e", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "468ad797-6e33-4284-b331-642f1512fc0f", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 3", "url": "https://meghanag.supporthive.com/kb/article/3-test-article-3/", "article_id": 3, "section_id": 3, "last_updated_at": "2025-04-16T06:34:30Z", "has_attachments": false, "attachment_count": 0}, "hash": "7de16eccb9a8b2cbc6f52743973366a2c2a4750f7755c5bafe60a7c0a0ef9174", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Having trouble logging into your account? Here are some common causes and\nquick fixes:\n\n1\\. Incorrect Username or Password****  \nDouble-check for typos. Passwords are case-sensitive.\n\n2\\. B<PERSON><PERSON> Cache and Cookies****  \nClear your browser's cache and cookies and try logging in again.\n\n3\\. Two-Factor Authentication Issues****  \nIf you're not receiving the OTP, check your spam folder or ensure your\nregistered phone number is correct.\n\n4\\. Account Lockouts  \nAfter multiple failed attempts, your account might be temporarily locked. Wait\n15 minutes before trying again.\n\nIf you're still having trouble, please contact support or reset your password\nusing the \"Forgot Password\" link.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 683, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4562066a-d60f-48e0-be05-7b7aa2181f1b": {"__data__": {"id_": "4562066a-d60f-48e0-be05-7b7aa2181f1b", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "0f07e832-e230-49a2-b70d-de494f138f00", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 4", "url": "https://meghanag.supporthive.com/kb/article/4-test-article-4/", "article_id": 4, "section_id": 2, "last_updated_at": "2025-04-16T06:36:18Z", "has_attachments": false, "attachment_count": 0}, "hash": "f346b26dd871456feb7dba1fa4d576fdb2bbcff9fed3ac65eb84b798f05cb4ff", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Title:\n\nContent:\n\nIf you've forgotten the answers to your security questions or wish to update\nthem, follow the steps below:\n\n1\\. Log into your account using your username and password.  \n2\\. Navigate to Settings > Security Settings.  \n3\\. Click on \"Reset Security Questions\".  \n4\\. Enter your current password for verification.  \n5\\. Choose new questions from the dropdown and provide new answers.  \n6\\. Click Save to update.\n\nNote: If you're unable to access your account, please click on “Need Help?” on\nthe login screen and select \"Contact Support\" to verify your identity and\nrequest a manual reset.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 604, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "aa731d84-3ee4-4eaa-92d9-61e45df1fb48": {"__data__": {"id_": "aa731d84-3ee4-4eaa-92d9-61e45df1fb48", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "1355c3ae-7391-4780-bd02-4f8dcc50fee0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "Test Article 5", "url": "https://meghanag.supporthive.com/kb/article/5-test-article-5/", "article_id": 5, "section_id": 2, "last_updated_at": "2025-04-16T07:44:33Z", "has_attachments": false, "attachment_count": 0}, "hash": "98a366847844471630118aeeff9aa1dbc676574bd387c0c200fcd0ed8f82bff2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Updating your profile ensures your contact information and preferences stay\ncurrent.\n\nSteps to update your profile:\n\n1\\. Log in to your Helpdesk account.  \n2\\. Click on your profile icon in the top-right corner.  \n3\\. Select “My Profile” from the dropdown menu.  \n4\\. Update the following fields as needed:  \n\\- Full Name  \n\\- Email Address  \n\\- Phone Number  \n\\- Preferred Language  \n5\\. Click “Save Changes” at the bottom of the page.\n\nNotes:  \n\\- If you're unable to update certain fields (like email), contact support for\nhelp.  \n\\- Your changes may take a few minutes to reflect across the system.\n\nFor privacy reasons, make sure your profile does not contain sensitive or\nincorrect information.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 700, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "658596c7-f634-496c-af1d-c15abe790746": {"__data__": {"id_": "658596c7-f634-496c-af1d-c15abe790746", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "ffb1f091-a2f1-4ac1-8e7c-7de27b597cc0", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "test image", "url": "https://meghanag.supporthive.com/kb/article/18-test-image/", "article_id": 18, "section_id": 2, "last_updated_at": "2025-05-13T04:34:16Z", "has_attachments": true, "attachment_count": 1}, "hash": "9f3ae6b5402b9047fc4eac154bf9ebf7ed5fa61b2281644279125ccbdb614b98", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "![](https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png)", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 159, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "7b80768e-93a8-413e-ba64-a7e795d9e357": {"__data__": {"id_": "7b80768e-93a8-413e-ba64-a7e795d9e357", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "6cbeebeb-d575-4246-a7ff-d732e0a3e897", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "embedded_image", "parent_article_id": 18, "parent_article_title": "test image", "attachment_id": "img_18_1", "attachment_name": "embedded_image_1.png", "attachment_url": "https://hf-\nstaging-v2.s3.amazonaws.com/hdpmeghanag_kb_attachments/2025/05-13/156de1d0-0a3e-4fbe-b6f1-fc888c203515/Screenshot_2025-05-13_at_10.03.18AM.png", "mime_type": "image/png", "file_extension": ".png", "file_size": 266051, "local_path": "./knowledge_base_indexes/cache/happyfox/attachments/article_18_image_img_18_1_embedded_image_1.png", "section_id": 2, "is_embedded_image": true}, "hash": "8c3809290019e7bbe83bdc51237895f769ee73bd21a42b0f346c7158c95f0a52", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Embedded Image: embedded_image_1.png\nFile Type: image/png\nSize: 266051 bytes\n\nReady for PremiumParser processing.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 113, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c3d58c2c-5cf4-444c-895e-3b85a0fbe5d6": {"__data__": {"id_": "c3d58c2c-5cf4-444c-895e-3b85a0fbe5d6", "embedding": null, "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "excluded_embed_metadata_keys": [], "excluded_llm_metadata_keys": [], "relationships": {"1": {"node_id": "046668cc-1391-49b2-8ad2-11364735dbf8", "node_type": "4", "metadata": {"source": "HappyFox", "content_type": "article", "title": "url sample", "url": "https://meghanag.supporthive.com/kb/article/21-url-sample/", "article_id": 21, "section_id": 2, "last_updated_at": "2025-05-16T04:09:12Z", "has_attachments": false, "attachment_count": 0}, "hash": "c7c848b616e153c8035355ea56b0a60c223b05bdeccd47754d93f9bd2a2ca173", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "<http://example.com>", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 20, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}}