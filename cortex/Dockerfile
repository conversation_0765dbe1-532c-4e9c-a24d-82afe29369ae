FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH="${PYTHONPATH}:/app"

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    git \
    netcat-traditional \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN pip install poetry
RUN poetry config virtualenvs.create false

# Copy poetry configuration files
COPY pyproject.toml /app/

# Install dependencies
RUN poetry install --no-root --no-interaction --no-ansi

# Copy project code
COPY . /app/

# Setup start script
RUN chmod +x /app/start.sh

# Run under a non-root user for security
RUN useradd -m appuser
USER appuser

# Default command
CMD ["/app/start.sh"]
