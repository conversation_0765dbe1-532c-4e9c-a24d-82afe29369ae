services:
  web:
    build:
      context: .
    restart: unless-stopped
    volumes:
      - .:/app
      - static_volume:/app/static
      - media_volume:/app/media
    healthcheck:
        test: ["CMD", "curl", "-f", "http://localhost:8000/readiness/"]
        interval: 60s
        timeout: 3s
        retries: 3
    env_file:
      - .env
    environment:
      - RUN_MIGRATIONS=true
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      celery_worker:
        condition: service_healthy
      opensearch:
        condition: service_healthy
    networks:
      - cortex_network
    ports:
      - "8000:8000"

  celery_worker:
    build:
      context: .
    command: celery -A cortex worker -l INFO -Q celery --concurrency=4
    volumes:
      - .:/app
      - static_volume:/app/static
      - media_volume:/app/media
    healthcheck:
      test: ["CMD-SHELL", "celery -A cortex inspect ping -d celery@$$HOSTNAME || exit 1"]
      interval: 10s
      timeout: 30s
      retries: 5
      start_period: 30s
    env_file:
      - .env
    environment:
      - CELERY_WORKER=true
      - MAX_WORKERS=8
      - MIN_WORKERS=2
      - MAX_TASKS_PER_WORKER=100
      - MAX_TIME_LIMIT_PER_TASK=3600
      - DJANGO_LOG_LEVEL=INFO
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - cortex_network

  db:
    image: postgres:16-alpine
    volumes:
      - ./postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env
    environment:
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_DB=${DATABASE_NAME}
    healthcheck: # healthcheck command for PostgreSQL service
      test: [ "CMD-SHELL", "pg_isready -U $$POSTGRES_USER" ]
      interval: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - cortex_network
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    healthcheck: # healthcheck command for Redis service
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 30s
      retries: 5
    restart: unless-stopped
    networks:
      - cortex_network
    ports:
      - "6379:6379"

  opensearch:
    image: opensearchproject/opensearch:2
    environment:
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m"
      - DISABLE_SECURITY_PLUGIN=true
      - action.auto_create_index=true
      - cluster.blocks.create_index=false
      - http.max_content_length=100mb
    ulimits:
      memlock:
        soft: -1
        hard: -1
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - opensearch_data:/usr/share/opensearch/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health?wait_for_status=yellow&timeout=50s" ]
      interval: 10s
      timeout: 60s
      retries: 5
    restart: unless-stopped
    networks:
      - cortex_network
    ports:
      - "9200:9200"
      - "9300:9300"

volumes:
  postgres_data:
  redis_data:
  opensearch_data:
  static_volume:
  media_volume:
  celerybeat_data:

networks:
  cortex_network:
    driver: bridge