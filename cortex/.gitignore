# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# .DS_Store
.DS_Store

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
node_modules/

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
test-results/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints
local_notebooks/

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule*
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# VSCode workspace settings
.vscode

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

postgres-data/
postgres-master-data/
postgres-slave-data/

.idea/
temp/

# docker-compose overrides for local
docker-compose.override.yml
# IDE-related configs
vetur.config.js
tsconfig.json
.history/

# VIM swap files
*.swp
*.swo

# Ignore credentials
credential*

# Environment files
.env
.env.*

# Static and media files
staticfiles/

# Ignore libs
libs/

# Ignore creds
token.json

# Ignore postgres data
postgres_data/

# Ignore references
references/

# Ignore docs
docs/
