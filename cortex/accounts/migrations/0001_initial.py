# Generated by Django 5.2 on 2025-05-08 05:54

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Account",
            fields=[
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("uuid", models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ("name", models.TextField()),
                (
                    "settings",
                    models.JSONField(blank=True, default=dict, help_text="Settings for the account", null=True),
                ),
                (
                    "chunking_strategy",
                    models.CharField(
                        choices=[
                            ("default", "Default Chunking"),
                            ("fixed_size", "Fixed-size Chunking"),
                            ("hierarchical", "Hierarchical Chunking"),
                            ("semantic", "Semantic Chunking"),
                            ("none", "No Chunking"),
                        ],
                        default="default",
                        help_text="Strategy for chunking documents",
                        max_length=50,
                    ),
                ),
                (
                    "chunking_config",
                    models.J<PERSON><PERSON>ield(
                        blank=True, default=dict, help_text="Configuration for the chunking strategy", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="deleted_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Account",
                "verbose_name_plural": "Accounts",
                "ordering": ["name"],
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
    ]
