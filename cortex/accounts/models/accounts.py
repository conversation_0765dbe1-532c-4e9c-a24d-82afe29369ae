from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.enum import ChunkingStrategy
from utils.models import CustomModel


class Account(CustomModel):
    name = models.TextField()
    settings = models.JSONField(default=dict, blank=True, null=True, help_text="Settings for the account")
    chunking_strategy = models.CharField(
        max_length=50,
        choices=ChunkingStrategy.choices,
        default=ChunkingStrategy.DEFAULT,
        help_text=_("Strategy for chunking documents"),
    )
    chunking_config = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Configuration for the chunking strategy")
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Account")
        verbose_name_plural = _("Accounts")
        ordering = ["name"]

    def __str__(self):
        return self.name
