from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from accounts.models import Account
from utils.admin import CustomModelAdmin


@admin.register(Account)
class AccountAdmin(CustomModelAdmin):
    list_display = ("name", "created_at", "updated_at")
    search_fields = ("name",)
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("name",)}),
        (_("Settings"), {"fields": ("settings", "chunking_strategy", "chunking_config"), "classes": ("collapse",)}),
        (_("Timestamps"), {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs
