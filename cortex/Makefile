.PHONY: help setup install dev test lint format docker-build docker-up docker-down docker-logs migrate collect-static clean

PYTHON = python
MANAGE = $(PYTHON) manage.py
POETRY = poetry
DOCKER_COMPOSE = docker compose
COMMAND = $(cmd)

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Setup the development environment
	@echo "Installing poetry if not present..."
	curl -sSL https://install.python-poetry.org | python3 -
	make install

# Dependencies
install: ## Install all dependencies
	$(POETRY) install

install-prod: ## Install production dependencies only
	$(POETRY) install --no-dev

# Development
dev: ## Run development server
	$(POETRY) run $(MANAGE) runserver 0.0.0.0:8000

shell: ## Run Django shell
	$(POETRY) run $(MANAGE) shell

lint: ## Run linting
	$(POETRY) run flake8 .
	poetry run mypy .

format: ## Format code
	$(POETRY) run black .
	$(POETRY) run isort .

# Docker commands
build: ## Build Docker images
	$(DOCKER_COMPOSE) build

start: ## Start Docker containers
	$(DOCKER_COMPOSE) up -d
	$(DOCKER_COMPOSE) logs -f

down: ## Stop Docker containers
	$(DOCKER_COMPOSE) down

logs: ## View logs from containers
	$(DOCKER_COMPOSE) logs -f

restart: ## Restart containers
	$(DOCKER_COMPOSE) restart
	$(DOCKER_COMPOSE) logs -f

prune: ## Remove all unused containers, networks, images
	docker system prune -a

# Django specific
migrate: ## Run database migrations
	@if [ -z "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)" ] || [ -z "$$(docker ps -q --no-trunc | grep "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)")" ]; then \
		echo "Web container is not running. Starting it..."; \
		$(DOCKER_COMPOSE) up -d web; \
		WEB_CONTAINER_STARTED=true; \
	fi
	$(DOCKER_COMPOSE) exec -it web $(MANAGE) migrate
	@if [ "$${WEB_CONTAINER_STARTED}" = "true" ]; then \
		echo "Stopping the web container that was started for this command..."; \
		$(DOCKER_COMPOSE) stop web; \
	fi

makemigrations: ## Create new migrations
	@if [ -z "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)" ] || [ -z "$$(docker ps -q --no-trunc | grep "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)")" ]; then \
		echo "Web container is not running. Starting it..."; \
		$(DOCKER_COMPOSE) up -d web; \
		WEB_CONTAINER_STARTED=true; \
	fi
	$(DOCKER_COMPOSE) exec -it web $(MANAGE) makemigrations
	@if [ "$${WEB_CONTAINER_STARTED}" = "true" ]; then \
		echo "Stopping the web container that was started for this command..."; \
		$(DOCKER_COMPOSE) stop web; \
	fi

collect-static: ## Collect static files
	@if [ -z "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)" ] || [ -z "$$(docker ps -q --no-trunc | grep "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)")" ]; then \
		echo "Web container is not running. Starting it..."; \
		$(DOCKER_COMPOSE) up -d web; \
		WEB_CONTAINER_STARTED=true; \
	fi
	$(DOCKER_COMPOSE) exec -it web $(MANAGE) collectstatic --no-input
	@if [ "$${WEB_CONTAINER_STARTED}" = "true" ]; then \
		echo "Stopping the web container that was started for this command..."; \
		$(DOCKER_COMPOSE) stop web; \
	fi

createsuperuser: ## Create a superuser
	@if [ -z "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)" ] || [ -z "$$(docker ps -q --no-trunc | grep "$$($(DOCKER_COMPOSE) ps -q web 2>/dev/null)")" ]; then \
		echo "Web container is not running. Starting it..."; \
		$(DOCKER_COMPOSE) up -d web; \
		WEB_CONTAINER_STARTED=true; \
	fi
	$(DOCKER_COMPOSE) exec -it web $(MANAGE) createsuperuser
	@if [ "$${WEB_CONTAINER_STARTED}" = "true" ]; then \
		echo "Stopping the web container that was started for this command..."; \
		$(DOCKER_COMPOSE) stop web; \
	fi

# Celery
celery-worker: ## Run Celery worker
	$(DOCKER_COMPOSE) exec -it web celery -A cortex worker -l INFO

celery-beat: ## Run Celery beat
	$(DOCKER_COMPOSE) exec -it web celery -A cortex beat -l INFO

command: ## Run a command in the container
	$(DOCKER_COMPOSE) exec -it web $(MANAGE) $(COMMAND)

# Utils
clean: ## Remove Python cache files
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name "*.egg" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type d -name ".coverage" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	docker system prune -a --filter "until=24h"

# Production
prod-deploy: ## Deploy to production
	@echo "Deploying to production..."
	$(DOCKER_COMPOSE) -f docker-compose.yml up -d --build

prod-collect-static: ## Collect static files in production
	$(DOCKER_COMPOSE) exec web python manage.py collectstatic --no-input

prod-migrate: ## Run migrations in production
	$(DOCKER_COMPOSE) exec web python manage.py migrate

# Default target
.DEFAULT_GOAL := help
