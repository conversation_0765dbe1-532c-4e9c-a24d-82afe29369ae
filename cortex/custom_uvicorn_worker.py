from uvicorn.workers import UvicornWorker as BaseUvicornWorker


class UvicornWorker(BaseUvicornWorker):
    """
    Custom Uvicorn worker with lifespan off. This is done to prevent the worker from
    raising an exception since Django does not support lifespan events.
    Ref > https://code.djangoproject.com/ticket/31508
    Ref > https://github.com/encode/uvicorn/issues/709
    """

    CONFIG_KWARGS = {"loop": "auto", "http": "auto", "lifespan": "off", "timeout_keep_alive": 300, "reload": True}
