# AI Services Django Project

This repository contains a Django-based AI services application with containerization using Docker and dependency management using Poetry.

## Environment Variables

Create a `.env` file in the project root with the following variables:

```
# Django Settings
DEBUG=False
SECRET_KEY=your-secret-key
ALLOWED_HOSTS=localhost,127.0.0.1
ENVIRONMENT=production  # Options: production, local
ADMIN_ENDPOINT=admin/
SESSION_COOKIE_SECURE=True

# Database Settings
DB_NAME=cortex
DB_USER=postgres
DB_PASSWORD=your-secure-password
DB_HOST=db
DB_PORT=5432

# Redis and Celery Settings
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Sentry Settings
SENTRY_DSN=your-sentry-dsn
SENTRY_REDACT_MODE=1

# OpenSearch Settings
OPENSEARCH_URL=http://opensearch:9200

# Langfuse Settings
ENABLE_LANGFUSE=False
LANGFUSE_SECRET_KEY=your-langfuse-secret-key
LANGFUSE_PUBLIC_KEY=your-langfuse-public-key
LANGFUSE_HOST=https://cloud.langfuse.com
```

## Development Setup

### Prerequisites

- Docker and Docker Compose
- Python 3.11 or higher
- Poetry

### Local Development Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd cortex
   ```

2. Create a `.env` file using the example above.

3. Install dependencies with Poetry:
   ```
   poetry install
   ```

4. Run the development server:
   ```
   poetry run python manage.py runserver
   ```

### Running with Docker

1. Build and start the containers:
   ```
   docker-compose up -d
   ```

2. Run migrations:
   ```
   docker-compose exec web python manage.py migrate
   ```

3. Create a superuser:
   ```
   docker-compose exec web python manage.py createsuperuser
   ```

4. Access the application at http://localhost:8000

## Architecture

The application is structured with the following services:
- **Web**: Django application served with Uvicorn/Gunicorn
- **Celery Worker**: For background task processing
- **Celery Beat**: For scheduled tasks
- **PostgreSQL**: Main database
- **Redis**: For caching and as message broker
- **OpenSearch**: For search functionality

## Database Configuration

### PostgreSQL

The project uses PostgreSQL 16 for the primary database. The connection settings are managed through environment variables.

Migration steps are automatically handled during container startup.

### OpenSearch

OpenSearch is configured for advanced search capabilities. The connection URL is provided through the `OPENSEARCH_URL` environment variable.

## Application Server and Async Processing

### Uvicorn Configuration

The project uses a custom Uvicorn worker class (`custom_uvicorn_worker.UvicornWorker`) to properly serve the Django application with lifespan handling disabled (required for Django).

### Redis and Celery

Redis is used for both caching and as a message broker for Celery tasks. Celery is configured with appropriate concurrency settings and structured logging.

## Monitoring and Logging

### Sentry Integration

Error tracking and performance monitoring are handled through Sentry. The connection is configured via the `SENTRY_DSN` environment variable.

### Structured Logging

The application uses structlog for JSON-formatted logging, making it easier to parse and analyze logs in production environments.

## Security Considerations

### Environment Configuration

All sensitive configuration is managed through environment variables, with appropriate defaults for development.

### Secrets Management

Docker secrets or environment variables should be used to manage credentials in production environments.

### Container Security

The Dockerfile includes security best practices:
- Running as a non-root user
- Minimal base image
- Secure dependency handling

## Production Deployment

For production deployment, set the following environment variables:
- `ENVIRONMENT=production`
- Set secure values for all credentials
- Configure proper SSL/TLS settings

Deploy using:
```
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## License

[Add your license information here]
