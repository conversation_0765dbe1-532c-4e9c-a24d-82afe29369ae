import logging

from celery import shared_task

from knowledge_bases.models import DataSource, IngestionJob
from knowledge_bases.services import (
    GoogleDriveIngestionPipeline,
    NotionIngestionPipeline,
    PDFIngestionPipeline,
    TextIngestionPipeline,
    WebpageIngestionPipeline,
)
from services import get_opensearch_vector_store, get_postgres_docstore
from utils.enum import DataSourceType, IngestionJobStatus

logger = logging.getLogger(__name__)


@shared_task
def process_ingestion_job(job_id: int) -> bool:
    """
    Process an ingestion job asynchronously.

    Args:
        job_id: The ID of the IngestionJob to process
    """
    try:
        # Get the job
        job = (
            IngestionJob.objects.select_related(
                "datasource",
                "datasource__knowledge_base",
                "datasource__knowledge_base__account",
            )
            .prefetch_related(
                "datasource__knowledge_base__account__topics_set",
            )
            .get(id=job_id)
        )
    except IngestionJob.DoesNotExist:
        logger.error("Ingestion job with ID %s not found", job_id)
        return False

    data_source = job.datasource

    # Update job status to processing
    job.status = IngestionJobStatus.PROCESSING
    job.save(update_fields=["status"])

    if data_source.type == DataSourceType.TEXT:
        pipeline = TextIngestionPipeline(data_source)
    elif data_source.type == DataSourceType.WEB_CRAWLER:
        pipeline = WebpageIngestionPipeline(data_source)
    elif data_source.type == DataSourceType.NOTION:
        pipeline = NotionIngestionPipeline(data_source)
    elif data_source.type == DataSourceType.GDRIVE:
        pipeline = GoogleDriveIngestionPipeline(data_source)
    elif data_source.type == DataSourceType.PDF:
        pipeline = PDFIngestionPipeline(data_source)
    else:
        logger.error("Unsupported data source type: %s", data_source.type)
        return False

    try:
        # Run the appropriate pipeline based on data source type
        pipeline.run()
    except Exception as e:
        logger.exception("Error processing ingestion job %s: %s", job_id, str(e))
        # Update job status to failed
        job.status = IngestionJobStatus.FAILED
        job.error_msg = str(e)
        job.save(update_fields=["status", "error_msg"])
        return False
    else:
        # Update job status to completed
        job.status = IngestionJobStatus.COMPLETED
        job.save(update_fields=["status"])
        logger.info("Successfully processed ingestion job %s for %s", job_id, data_source.name)
        return True


@shared_task
def delete_data_source_docstore_for_bot(data_source_id: int):
    """
    Delete the data source with the given id from the docstore
    """
    try:
        data_source = DataSource.objects.get(id=data_source_id)
    except DataSource.DoesNotExist:
        return

    logger.info("Deleting data source %s from docstore", data_source.id)

    docstore = get_postgres_docstore(
        account_id=data_source.knowledge_base.account_id,
        data_source_id=data_source.id,
    )
    vector_store = get_opensearch_vector_store()

    existing_doc_ids_before = set(docstore.get_all_document_hashes().values())

    logger.info("Deleting %d documents", len(existing_doc_ids_before), extra={"doc_ids": existing_doc_ids_before})

    for ref_doc_id in existing_doc_ids_before:
        docstore.delete_document(ref_doc_id)
        vector_store.delete(ref_doc_id)

    logger.info("Deleting data source %s from docstore completed", data_source.id)
