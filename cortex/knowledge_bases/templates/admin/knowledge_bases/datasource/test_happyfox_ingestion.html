{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<style>
    .form-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .form-row {
        margin-bottom: 15px;
    }
    .form-row label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .form-row input[type="text"],
    .form-row input[type="url"],
    .form-row input[type="password"],
    .form-row select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .help-text {
        color: #666;
        font-size: 0.9em;
        margin-top: 5px;
    }
    .error-message {
        color: #ba2121;
        margin-top: 5px;
    }
    .submit-row {
        margin-top: 20px;
        text-align: right;
    }
    .submit-row input {
        background: #417690;
        color: #fff;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .submit-row input:hover {
        background: #295570;
    }
    #results {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        display: none;
    }
    .success {
        color: #28a745;
    }
    .error {
        color: #dc3545;
    }
    .logs {
        margin-top: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
    }
    .info-box {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .info-box h3 {
        margin-top: 0;
        color: #0066cc;
    }
    .credentials-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin: 15px 0;
    }
    .credentials-section h4 {
        margin-top: 0;
        color: #856404;
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{% trans "Test HappyFox Ingestion" %}</h2>
    
    <div class="info-box">
        <h3>{% trans "About HappyFox Ingestion" %}</h3>
        <p>{% trans "This tool allows you to test the HappyFox knowledge base ingestion functionality. It will fetch articles from HappyFox sections and index them into the vector store." %}</p>
        <ul>
            <li>{% trans "Select an account to associate the knowledge base with" %}</li>
            <li>{% trans "Provide a knowledge base name and data source name" %}</li>
            <li>{% trans "Enter your HappyFox staff login credentials (domain, username, password, and optionally app ID)" %}</li>
            <li>{% trans "Optionally specify section IDs to filter articles (leave empty for all sections)" %}</li>
            <li><strong>{% trans "Attachment Processing:" %}</strong> {% trans "Downloads and processes PDF, image, and document attachments using Cortex parsers" %}</li>
        </ul>
        <p><strong>{% trans "Note:" %}</strong> {% trans "Your credentials are used only for this session and are not stored." %}</p>
        <p><strong>{% trans "Enhanced Features:" %}</strong> {% trans "This integration now processes both article text and attached documents for comprehensive content extraction." %}</p>
    </div>
    
    <form method="post" id="happyfoxIngestionForm">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="form-row">
            {% if field.name == "happyfox_domain" %}
                <div class="credentials-section">
                    <h4>{% trans "HappyFox OAuth Authentication" %}</h4>
                    <p class="help">Choose your authentication method: OAuth access token (preferred) or OAuth app credentials for authorization flow.</p>
            {% endif %}

            {% if field.name == "happyfox_oauth_token" %}
                </div>
                <div class="credentials-section">
                    <h4>{% trans "OAuth Access Token (Preferred)" %}</h4>
                    <p class="help">If you have completed the OAuth authorization code flow, enter your access token here.</p>
            {% endif %}

            {% if field.name == "happyfox_app_id" %}
                </div>
                <div class="credentials-section">
                    <h4>{% trans "OAuth App Credentials (For Authorization Flow)" %}</h4>
                    <p class="help">Use these credentials to complete the OAuth authorization code flow if you don't have an access token.</p>
            {% endif %}

            {{ field.label_tag }}
            {{ field }}
            {% if field.help_text %}
            <div class="help-text">{{ field.help_text }}</div>
            {% endif %}
            {% if field.errors %}
            <div class="error-message">{{ field.errors }}</div>
            {% endif %}

            {% if field.name == "happyfox_signing_secret" %}
                </div>
                <div class="credentials-section">
                    <h4>{% trans "Staff Credentials (Fallback)" %}</h4>
                    <p class="help">Staff credentials are used as fallback if no OAuth access token is provided. Combined with app context for OAuth compliance.</p>
            {% endif %}

            {% if field.name == "happyfox_password" %}
                </div>
            {% endif %}
        </div>
        {% endfor %}

        <div class="submit-row">
            <input type="submit" value="{% trans 'Process HappyFox Articles' %}" id="submitButton">
        </div>
    </form>

    <div id="results"></div>
</div>

<script>
document.getElementById('happyfoxIngestionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitButton = document.getElementById('submitButton');
    const resultsDiv = document.getElementById('results');
    
    // Disable submit button and show loading state
    submitButton.disabled = true;
    submitButton.value = '{% trans "Processing..." %}';
    
    // Clear previous results
    resultsDiv.innerHTML = '';
    resultsDiv.style.display = 'none';
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        resultsDiv.style.display = 'block';
        
        if (data.status === 'success' || data.status === 'Success') {
            resultsDiv.innerHTML = `
                <div class="success">${data.message}</div>
                <div class="logs">${data.logs}</div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="error">${data.message}</div>
                <div class="logs">${data.logs}</div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = `
            <div class="error">An error occurred: ${error.message}</div>
        `;
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.value = '{% trans "Process HappyFox Articles" %}';
    });
});
</script>
{% endblock %}
