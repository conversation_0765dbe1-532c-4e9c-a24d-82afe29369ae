{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<style>
    .form-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .form-row {
        margin-bottom: 15px;
    }
    .form-row label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .form-row input[type="text"],
    .form-row input[type="file"],
    .form-row select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .help-text {
        color: #666;
        font-size: 0.9em;
        margin-top: 5px;
    }
    .error-message {
        color: #ba2121;
        margin-top: 5px;
    }
    .submit-row {
        margin-top: 20px;
        text-align: right;
    }
    .submit-row input {
        background: #417690;
        color: #fff;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .submit-row input:hover {
        background: #295570;
    }
    #results {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        display: none;
    }
    .success {
        color: #28a745;
    }
    .error {
        color: #dc3545;
    }
    .logs {
        margin-top: 10px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        font-family: monospace;
        white-space: pre-wrap;
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <h2>{% trans "Test PDF Ingestion" %}</h2>
    
    <form method="post" enctype="multipart/form-data" id="pdfIngestionForm">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="form-row">
            {{ field.label_tag }}
            {{ field }}
            {% if field.help_text %}
            <div class="help-text">{{ field.help_text }}</div>
            {% endif %}
            {% if field.errors %}
            <div class="error-message">{{ field.errors }}</div>
            {% endif %}
        </div>
        {% endfor %}

        <div class="submit-row">
            <input type="submit" value="{% trans 'Process PDF' %}" id="submitButton">
        </div>
    </form>

    <div id="results"></div>
</div>

<script>
document.getElementById('pdfIngestionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitButton = document.getElementById('submitButton');
    const resultsDiv = document.getElementById('results');
    
    // Disable submit button and show loading state
    submitButton.disabled = true;
    submitButton.value = '{% trans "Processing..." %}';
    
    // Clear previous results
    resultsDiv.innerHTML = '';
    resultsDiv.style.display = 'none';
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        resultsDiv.style.display = 'block';
        
        if (data.status === 'success') {
            resultsDiv.innerHTML = `
                <div class="success">${data.message}</div>
                <div class="logs">${data.logs}</div>
            `;
        } else {
            resultsDiv.innerHTML = `
                <div class="error">${data.message}</div>
                <div class="logs">${data.logs}</div>
            `;
        }
    })
    .catch(error => {
        resultsDiv.style.display = 'block';
        resultsDiv.innerHTML = `
            <div class="error">An error occurred: ${error.message}</div>
        `;
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.value = '{% trans "Process PDF" %}';
    });
});
</script>
{% endblock %} 