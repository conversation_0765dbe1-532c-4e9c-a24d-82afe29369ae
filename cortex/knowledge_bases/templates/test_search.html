{% extends 'admin/base_site.html' %}
{% load i18n static %}

{% block extrastyle %}
<style>
    /* CSS Variables for light/dark mode compatibility */
    :root {
        --bg-primary: #ffffff;
        --bg-secondary: #f9f9f9;
        --bg-tertiary: #f0f7ff;
        --text-primary: #333333;
        --text-secondary: #666666;
        --text-muted: #888888;
        --border-color: #dddddd;
        --border-light: #eeeeee;
        --accent-color: #0066cc;
        --accent-hover: #004c99;
        --shadow-color: rgba(0,0,0,0.08);
        --score-high: #2e8b57;
        --score-medium: #ff8c00;
        --score-low: #dc3545;
    }
    
    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        :root {
            --bg-primary: #1e1e1e;
            --bg-secondary: #2d2d2d;
            --bg-tertiary: #1a2b42;
            --text-primary: #e0e0e0;
            --text-secondary: #b0b0b0;
            --text-muted: #909090;
            --border-color: #444444;
            --border-light: #333333;
            --accent-color: #4d8edb;
            --accent-hover: #6ba3e8;
            --shadow-color: rgba(0,0,0,0.2);
            --score-high: #4caf50;
            --score-medium: #ff9800;
            --score-low: #f44336;
        }
    }
    
    /* Main layout */
    .search-container {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
    }
    
    .search-form {
        flex: 1;
        min-width: 350px;
    }
    
    .search-info {
        flex: 1;
        min-width: 350px;
        background: var(--bg-secondary);
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px var(--shadow-color);
        color: var(--text-primary);
    }
    
    .search-info h2, .search-info h3 {
        color: var(--text-primary);
    }
    
    /* Form styling */
    .module.aligned {
        background: var(--bg-primary);
        border-radius: 8px;
        box-shadow: 0 2px 8px var(--shadow-color);
        overflow: hidden;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 0px;
    }
    
    fieldset.module.aligned {
        border: none;
        padding: 20px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .form-row {
        margin-bottom: 15px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .field-box {
        width: 100%;
        box-sizing: border-box;
    }
    
    .field-box label {
        display: block;
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--text-primary);
    }
    
    .field-box input[type="text"],
    .field-box input[type="number"],
    .field-box textarea,
    .field-box select {
        width: 100%;
        max-width: 100%;
        padding: 8px 12px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
        background-color: var(--bg-primary);
        color: var(--text-primary);
        box-sizing: border-box;
    }
    
    /* Fix for knowledge base dropdown */
    .field-box select {
        height: auto;
        min-height: 38px;
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
    }
    
    .field-box textarea {
        min-height: 100px;
    }
    
    .help {
        color: var(--text-secondary);
        font-size: 12px;
        margin-top: 4px;
    }
    
    /* Results styling */
    .results-container {
        margin-top: 20px;
        background: var(--bg-primary);
        border-radius: 8px;
        padding: 25px;
        box-shadow: 0 2px 8px var(--shadow-color);
    }
    
    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--border-light);
    }
    
    .results-header h2 {
        color: var(--text-primary);
    }
    
    .results-count {
        background: var(--bg-tertiary);
        color: var(--accent-color);
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
    }
    
    .result-item {
        margin-bottom: 20px;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid var(--accent-color);
        background: var(--bg-secondary);
        box-shadow: 0 1px 3px var(--shadow-color);
        transition: all 0.2s ease;
    }
    
    .result-item:hover {
        box-shadow: 0 3px 8px var(--shadow-color);
    }
    
    .result-item h3 {
        margin-top: 0;
        color: var(--text-primary);
        font-size: 16px;
        display: flex;
        align-items: center;
    }
    
    .result-number {
        display: inline-block;
        background: var(--accent-color);
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        margin-right: 10px;
        font-size: 12px;
    }
    
    .result-text {
        margin: 10px 0;
    }
    
    .result-text pre {
        background: var(--bg-primary);
        padding: 12px;
        border-radius: 4px;
        border: 1px solid var(--border-light);
        overflow-x: auto;
        font-size: 13px;
        line-height: 1.5;
        color: var(--text-primary);
        width: 100%;
        box-sizing: border-box;
        max-width: 100%;
        word-wrap: break-word;
    }
    
    .result-metadata {
        font-size: 0.85em;
        margin-top: 10px;
        color: var(--text-secondary);
        background: var(--bg-secondary);
        padding: 10px;
        border-radius: 4px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .result-metadata pre {
        margin: 5px 0;
        white-space: pre-wrap;
        font-size: 12px;
        color: var(--text-primary);
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
        word-wrap: break-word;
        box-sizing: border-box;
    }
    
    .result-score {
        margin-top: 10px;
        padding-top: 8px;
        border-top: 1px dashed var(--border-light);
    }
    
    .score-high {
        color: var(--score-high);
        font-weight: bold;
    }
    
    .score-medium {
        color: var(--score-medium);
        font-weight: bold;
    }
    
    .score-low {
        color: var(--score-low);
        font-weight: bold;
    }
    
    /* Submit button */
    .submit-row {
        padding: 15px 20px;
        background: var(--bg-secondary);
        text-align: right;
    }
    
    .submit-row input[type="submit"] {
        background: var(--accent-color);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 600;
        transition: background 0.2s;
    }
    
    .submit-row input[type="submit"]:hover {
        background: var(--accent-hover);
    }
    
    /* Reranking toggle styling */
    .reranking-toggle {
        margin-top: 15px;
        padding: 15px;
        background: var(--bg-tertiary);
        border-radius: 6px;
        border-left: 4px solid var(--accent-color);
        color: var(--text-primary);
    }
    
    .reranking-toggle h3 {
        margin-top: 0;
        font-size: 16px;
        color: var(--accent-color);
    }
    
    /* No results message */
    .no-results {
        padding: 30px;
        text-align: center;
        color: var(--text-secondary);
    }
    
    .no-results h3 {
        color: var(--text-primary);
    }
    
    .no-results-icon {
        font-size: 40px;
        margin-bottom: 15px;
        color: var(--text-muted);
    }
    
    /* Timing information styling */
    .timing-info {
        margin: 0 0 20px 0;
        padding: 15px;
        background: var(--bg-secondary);
        border-radius: 6px;
        border-left: 4px solid var(--accent-color);
    }
    
    .timing-header h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 16px;
        color: var(--accent-color);
    }
    
    .timing-details {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .timing-metric {
        padding: 8px 12px;
        background: var(--bg-primary);
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 1px 2px var(--shadow-color);
    }
    
    .metric-label {
        font-weight: 600;
        margin-right: 8px;
        color: var(--text-primary);
    }
    
    .metric-value {
        color: var(--accent-color);
        font-family: monospace;
        font-size: 14px;
    }
    
    .total-time {
        background: var(--bg-tertiary);
        border: 1px solid var(--accent-color);
    }

    .error-message {
        color: var(--score-low);
        background-color: #fdd; /* Light red background */
        border: 1px solid var(--score-low);
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 4px;
    }

    .llm-answer-section {
        background: #e8f5e8;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .llm-answer-header h3 {
        color: #155724;
        margin: 0 0 10px 0;
        font-size: 1.2em;
    }

    .llm-disclaimer {
        font-size: 0.85em;
        color: #6c757d;
        font-style: italic;
        margin-bottom: 15px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 3px;
        border-left: 3px solid #007bff;
    }

    .llm-answer-content {
        background: white;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #d4edda;
    }

    .llm-answer-content pre {
        margin: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
        color: #155724;
        line-height: 1.6;
    }

    .sources-header {
        margin: 20px 0 15px 0;
        padding-top: 15px;
        border-top: 2px solid #dee2e6;
    }

    .sources-header h3 {
        color: #495057;
        margin: 0;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label='knowledge_bases' %}">{% trans 'Knowledge Bases' %}</a>
    &rsaquo; <a href="{% url 'admin:knowledge_bases_knowledgebase_changelist' %}">{% trans 'Knowledge Base' %}</a>
    &rsaquo; {% trans 'Test Search' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">

    {% if error_message %}
    <div class="error-message">
        {{ error_message }}
    </div>
    {% endif %}

    <div class="search-container">
        <div class="search-form">
            <div class="module aligned">
                <form method="post">
                    {% csrf_token %}
                    <fieldset class="module aligned">
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_account">{% trans 'Account:' %}</label>
                                {{ form.account }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_name">{% trans 'Knowledge Base Name:' %}</label>
                                {{ form.name }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_slug">{% trans 'Knowledge Base Slug:' %}</label>
                                {{ form.slug }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_query_text">{% trans 'Query:' %}</label>
                                {{ form.query_text }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_similarity_threshold">{% trans 'Similarity Threshold:' %}</label>
                                {{ form.similarity_threshold }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_top_k">{% trans 'Top K Results:' %}</label>
                                {{ form.top_k }}
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="field-box">
                                <label for="id_permissions">{% trans 'Permissions (JSON):' %}</label>
                                {{ form.permissions }}
                                <div class="help">{{ form.permissions.help_text }}</div>
                            </div>
                        </div>
                        
                        <div class="reranking-toggle">
                            <h3>{% trans 'Reranking Options' %}</h3>
                            <p>{% trans 'Reranking uses LLM to improve search result relevance' %}</p>
                            <div class="form-row">
                                <div class="field-box">
                                    <label for="id_use_reranking">{% trans 'Enable Reranking:' %}</label>
                                    {{ form.use_reranking }}
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="field-box">
                                    <label for="id_rerank_top_n">{% trans 'Rerank Top N:' %}</label>
                                    {{ form.rerank_top_n }}
                                    <div class="help">{{ form.rerank_top_n.help_text }}</div>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                    <div class="submit-row">
                        <input type="submit" value="{% trans 'Search' %}" class="default" />
                    </div>
                </form>
            </div>
        </div>
        
        <div class="search-info">
            <h2>{% trans 'Knowledge Base Search Testing' %}</h2>
            <p>{% trans 'This tool allows you to test search functionality against your knowledge bases.' %}</p>
            
            <h3>{% trans 'How it works:' %}</h3>
            <ul>
                <li>{% trans 'Select an Account' %}</li>
                <li>{% trans 'Enter the Knowledge Base Slug' %}</li>
                <li>{% trans 'Enter your query in natural language' %}</li>
                <li>{% trans 'Adjust similarity threshold (0-1) to filter results' %}</li>
                <li>{% trans 'Set the maximum number of results to return' %}</li>
                <li>{% trans 'Optionally enable reranking for improved relevance' %}</li>
            </ul>
            
            <h3>{% trans 'About Reranking:' %}</h3>
            <p>{% trans 'Reranking uses Claude LLM to evaluate and reorder search results based on semantic relevance to your query, potentially improving result quality.' %}</p>
        </div>
    </div>
    
    {% if search_results %}
    <div class="results-container">
        <div class="results-header">
            <h2>{% trans 'Search Results' %}</h2>
            <span class="results-count">{{ search_results|length }} {% trans 'results found' %}</span>
        </div>
        
        {% if timing_info %}
        <div class="timing-info">
            <div class="timing-header">
                <h3>{% trans 'Performance Metrics' %}</h3>
            </div>
            <div class="timing-details">
                <div class="timing-metric">
                    <span class="metric-label">{% trans 'Search Time:' %}</span>
                    <span class="metric-value">{{ timing_info.search_time }} {% trans 'seconds' %}</span>
                </div>
                
                {% if timing_info.use_reranking %}
                <div class="timing-metric">
                    <span class="metric-label">{% trans 'Reranking Time:' %}</span>
                    <span class="metric-value">{{ timing_info.rerank_time }} {% trans 'seconds' %}</span>
                </div>
                {% endif %}
                
                <div class="timing-metric total-time">
                    <span class="metric-label">{% trans 'Total Time:' %}</span>
                    <span class="metric-value">{{ timing_info.total_time }} {% trans 'seconds' %}</span>
                </div>
            </div>
        </div>
        {% endif %}

        {% if search_results.0.llm_answer %}
        <div class="llm-answer-section">
            <div class="llm-answer-header">
                <h3>🤖 {% trans 'AI Assistant Answer' %}</h3>
                <div class="llm-disclaimer">
                    ℹ️ {% trans 'This answer is generated based strictly on the knowledge base articles shown below. No external information is used.' %}
                </div>
            </div>
            <div class="llm-answer-content">
                <pre>{{ search_results.0.llm_answer }}</pre>
            </div>
        </div>

        <div class="sources-header">
            <h3>📚 {% trans 'Source Articles' %}</h3>
        </div>
        {% endif %}

        {% for result in search_results %}
            <div class="result-item">
                <h3><span class="result-number">{{ forloop.counter }}</span> {% trans 'Result' %}</h3>
                <div class="result-text">
                    <pre>{{ result.text }}</pre>
                </div>
                <div class="result-metadata">
                    <strong>{% trans 'Metadata:' %}</strong>
                    <pre>{{ result.metadata }}</pre>
                    {% if result.score is not None %}
                    <div class="result-score">
                        <strong>{% trans 'Relevance Score:' %}</strong> 
                        <span class="{% if result.score > 0.7 %}score-high{% elif result.score > 0.4 %}score-medium{% else %}score-low{% endif %}">
                            {{ result.score|floatformat:4 }}
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if not search_results and request.method == 'POST' and not error_message %}
    <div class="results-container">
        <div class="no-results">
            <div class="no-results-icon">🔍</div>
            <h3>{% trans 'No Results Found' %}</h3>
            <p>{% trans 'No matching documents were found for your query. Try adjusting your search terms or lowering the similarity threshold.' %}</p>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}