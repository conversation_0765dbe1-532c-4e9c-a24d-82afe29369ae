import logging
from typing import List
from pathlib import Path

from llama_index.core.ingestion import Doc<PERSON>reStrategy, IngestionPipeline
from llama_index.core.schema import Document as LlamaDocument, NodeRelationship, ObjectType

from accounts.models import Account
from knowledge_bases.models import DataSource
from knowledge_bases.models import Document as DataSourceDocument
from knowledge_bases.services.ingestion_pipeline.base import BaseIngestionPipeline
from services import TagTopicsForContentTransformComponent, get_opensearch_vector_store, get_postgres_docstore, get_cohere_embedding_model
from utils.enum import DocumentStatus
from utils.readers.pdf import PDFReader 

logger = logging.getLogger(__name__)


class PDFIngestionPipeline(BaseIngestionPipeline):
    """
    Ingestion pipeline for PDF documents.
    """

    def __init__(self, data_source: DataSource, account: Account) -> None:
        super().__init__(data_source)
        self.pdf_reader = PDFReader()
        self.account = account

    def _store_documents(self, documents: List[LlamaDocument]) -> None:
        """
        Store the processed documents in the vector store and docstore.
        """
        # Get the vector store and docstore
        vector_store = get_opensearch_vector_store()
        docstore = get_postgres_docstore(
            account_id=self.account.uuid,
            data_source_id=self.data_source.id
        )

        # Store documents in docstore
        docstore.add_documents(documents)

        # Create nodes from documents
        nodes = []
        for doc in documents:
            node = LlamaDocument(
                text=doc.text,
                metadata=doc.metadata,
                relationships={
                    NodeRelationship.SOURCE: {
                        "node_id": doc.id_,
                        "node_type": ObjectType.DOCUMENT,
                    }
                },
                excluded_llm_metadata_keys=["file_name", "file_type", "file_size"],
                excluded_embed_metadata_keys=["file_name", "file_type", "file_size"],
                object_type=ObjectType.DOCUMENT,
            )
            nodes.append(node)

        # Get the embedding model from the vector store
        embed_model = get_cohere_embedding_model()
        
        # Set embeddings for all nodes
        for node in nodes:
            node.embedding = embed_model.get_text_embedding(node.text)

        # Store nodes in vector store
        vector_store.add(nodes)

    def process_file(self, pdf_file, document: DataSourceDocument) -> None:
        """
        Process a PDF file directly from the uploaded file.
        """
        # Read the file content
        content = pdf_file.read()
        
        # Process the PDF content
        llama_docs = self.pdf_reader.load_data_from_bytes(content, pdf_file.name)
        
        # Update document metadata
        for llama_doc in llama_docs:
            llama_doc.id_ = f"{document.data_source_id}_{document.id}"
            llama_doc.metadata.update({
                "id": document.id,
                "data_source_id": self.data_source.id,
                "source": "pdf",
            })
            for key, value in document.metadata.get("permissions", {}).items():
                llama_doc.metadata[f"{self.account.uuid}__permissions_{key}"] = value

        # Store the processed documents
        self._store_documents(llama_docs)
        
        # Update document status
        document.status = DocumentStatus.PROCESSED
        document.save()

    def _parse_document(self, doc: DataSourceDocument) -> List[LlamaDocument]:
        """
        Parse a PDF document into a list of Llama Documents.
        """
        file_path = Path(doc.metadata["file_path"])
        logger.info("Loading data from PDF: %s", file_path)
        llama_docs = self.pdf_reader.load_data(file_path)
        logger.info("Loaded data from PDF: %s, total Llama documents: %s", file_path, len(llama_docs))

        for llama_doc in llama_docs:
            llama_doc.id_ = f"{doc.data_source_id}_{doc.id}" 
            llama_doc.metadata.update({
                "id": doc.id,
                "data_source_id": self.data_source.id,
                "source": "pdf",
            })
            for key, value in doc.metadata.get("permissions", {}).items():
                llama_doc.metadata[f"{self.account.uuid}__permissions_{key}"] = value

        return llama_docs

    def _get_documents_to_ingest(self) -> List[LlamaDocument]:
        """
        Get the PDF documents to ingest for the data source.
        """
        documents = []
        documents_to_process = self.data_source.documents.filter(status=DocumentStatus.PENDING, is_active=True)

        if not documents_to_process:
            logger.info("No PDF documents to process")
            return documents

        logger.info("Processing %s PDF documents", documents_to_process.count())

        for content in documents_to_process:
            try:
                if "file_path" not in content.metadata:
                    logger.warning(f"Document {content.id} is missing 'file_path' metadata. Skipping.")
                    continue
                
                parsed_docs = self._parse_document(content)
                documents.extend(parsed_docs)
            except FileNotFoundError as e:
                logger.error(f"Error processing PDF document {content.id}: {e}. Marking as failed.")
                content.status = DocumentStatus.FAILED
                content.save()
            except Exception as e:
                logger.error(f"Unexpected error processing PDF document {content.id}: {e}")
                content.status = DocumentStatus.FAILED
                content.save()
        return documents

    def run(self):
        """
        Run the ingestion pipeline for PDF documents.

        Creates an IngestionPipeline with the appropriate node parser and transformations
        for processing PDF content.

        Returns:
            None
        """
        # Get the account from the knowledge base
        account = self.knowledge_base.account
        node_parser = self._get_node_parser()
        logger.info("Node parser: %s", node_parser)

        pipeline = IngestionPipeline(
            transformations=[
                node_parser,
                TagTopicsForContentTransformComponent(account=account),
                self.embed_model,
            ],
            docstore=get_postgres_docstore(
                account_id=account.uuid,
                data_source_id=self.data_source.id,
            ),
            vector_store=get_opensearch_vector_store(),
            docstore_strategy=DocstoreStrategy.UPSERTS_AND_DELETE,
        )

        logger.info("Ingesting PDF documents for data source id: %s", self.data_source.id)
        documents = self._get_documents_to_ingest()
        logger.info("Total PDF documents to ingest: %s", len(documents))

        # Add debug logging to inspect document content
        for i, doc in enumerate(documents):
            logger.info("PDF Document %s content length: %s characters", i + 1, len(doc.text))
            logger.info("PDF Document %s metadata: %s", i + 1, doc.metadata)
            # Log a short preview of the content (first 200 chars)
            preview = doc.text[:200] + "..." if len(doc.text) > 200 else doc.text
            logger.info("PDF Document content preview: %s", preview)

        logger.info("Running PDF ingestion pipeline")
        nodes = pipeline.run(
            show_progress=True,
            documents=documents,
        )
        logger.info("PDF Ingestion pipeline completed, total nodes: %s", len(nodes))

        doc_to_nodes = {}
        for node in nodes:
            logger.info("Node: %s", node)
            for rel_type, rel_node in node.relationships.items():
                logger.info("Relationship type: %s, Relationship node: %s", rel_type, rel_node)
                if rel_type == NodeRelationship.SOURCE:
                    logger.info("Relationship type is SOURCE")
                    if rel_node.node_type == ObjectType.DOCUMENT:
                        logger.info("Relationship node is a DOCUMENT")
                        if rel_node.node_id not in doc_to_nodes:
                            logger.info("Adding relationship node to doc_to_nodes")
                            doc_to_nodes[rel_node.node_id] = []
                        doc_to_nodes[rel_node.node_id].append(node)

        self._update_data_source_content(documents, doc_to_nodes)
