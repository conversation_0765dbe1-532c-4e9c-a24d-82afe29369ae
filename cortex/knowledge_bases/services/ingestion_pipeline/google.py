import logging
from typing import List

from llama_index.core.ingestion import <PERSON><PERSON><PERSON><PERSON>trategy, IngestionPipeline
from llama_index.core.schema import Document, NodeRelationship, ObjectType
from llama_index.readers.google import GoogleDriveReader

from knowledge_bases.models import DataSource
from knowledge_bases.models import Document as DataSourceDocument
from knowledge_bases.services.ingestion_pipeline.base import BaseIngestionPipeline
from services import TagTopicsForContentTransformComponent, get_opensearch_vector_store, get_postgres_docstore
from utils.enum import DocumentStatus

logger = logging.getLogger(__name__)


class GoogleDriveIngestionPipeline(BaseIngestionPipeline):
    """
    Ingestion pipeline for Google Drive content.
    """

    def __init__(self, data_source: DataSource) -> None:
        """Initialize the Google Drive ingestion pipeline."""
        super().__init__(data_source)
        self.authorized_user_info = data_source.credentials
        if not self.authorized_user_info:
            raise ValueError("Google Drive credentials are required")

    def _parse_document(self, doc: DataSourceDocument) -> list[Document]:
        """
        Parse a Google Drive document into a list of nodes.
        """

        def extract_metadata(resource_id: str, resource_type: str) -> dict:
            """Extract metadata from a Google Drive file."""
            return {
                "id": doc.id,
                "data_source_id": self.data_source.id,
                "title": doc.title or "Untitled Google Drive Document",
                "source": "google_drive",
                "resource_id": resource_id,
                "resource_type": resource_type,
            }

        # Extract resource_id and resource_type from document metadata
        resource_id = doc.metadata.get("resource_id")
        resource_type = doc.metadata.get("resource_type")
        if not resource_id or not resource_type:
            logger.warning("Document %s has no resource id or resource type in metadata, skipping", doc.id)
            return []

        # We pass empty list for folder_id as we're using specific file IDs
        reader = GoogleDriveReader(
            authorized_user_info=self.authorized_user_info,
        )
        if resource_type == "file":
            logger.info("Loading data from Google Drive file: %s", resource_id)
            drive_docs = reader.load_data(file_ids=[resource_id])
            logger.info(
                "Loaded data from Google Drive file: %s, total docs: %s",
                resource_id,
                len(drive_docs) if drive_docs else 0,
            )
        elif resource_type == "folder":
            logger.info("Loading data from Google Drive folder: %s", resource_id)
            drive_docs = reader.load_data(folder_id=resource_id)
            logger.info(
                "Loaded data from Google Drive folder: %s, total docs: %s",
                resource_id,
                len(drive_docs) if drive_docs else 0,
            )
        else:
            logger.warning("Invalid resource type: %s", resource_type)
            return []

        if not drive_docs:
            logger.warning("No content retrieved for Google Drive %s %s", resource_type, resource_id)
            return []

        for document in drive_docs:
            # Attach proper ID to the document
            document.id_ = f"{doc.data_source_id}_{resource_id}"
            # Add any additional metadata
            for key, value in extract_metadata(resource_id, resource_type).items():
                document.metadata[key] = value

            for key, value in doc.metadata.get("permissions", {}).items():
                document.metadata[f"{self.account.uuid}__permissions_{key}"] = value

        return drive_docs

    def _get_documents_to_ingest(self) -> List[Document]:
        """
        Get the documents to ingest from the data source.
        This method extracts Google Drive file IDs from document metadata and uses them to fetch content.
        """
        documents = []
        # Get all documents that need to be processed
        documents_to_process = self.data_source.documents.filter(status=DocumentStatus.PENDING, is_active=True)

        if not documents_to_process:
            logger.info("No documents to process")
            return documents

        logger.info("Processing %s documents", documents_to_process.count())

        # Process each document to extract file IDs and fetch content
        for doc in documents_to_process:
            try:
                # Get file ID from metadata
                if doc.metadata.get("resource_id"):
                    drive_docs = self._parse_document(doc)
                    if drive_docs:
                        # Update the document with content from Google Drive
                        doc.raw_content = drive_docs[0].text
                        doc.title = drive_docs[0].metadata.get("title", "Untitled Google Drive Document")
                        doc.save(update_fields=["raw_content", "title"])
                        documents.extend(drive_docs)
                    else:
                        logger.warning(
                            "No content fetched for Google Drive resource: %s", doc.metadata.get("resource_id")
                        )
                        doc.status = DocumentStatus.FAILED
                        doc.error_message = "No content fetched for Google Drive resource"
                        doc.save(update_fields=["status", "error_message"])
                else:
                    logger.warning("Document %s has no resource_id in metadata, skipping", doc.id)
                    doc.status = DocumentStatus.FAILED
                    doc.error_message = "No resource_id in metadata"
                    doc.save(update_fields=["status", "error_message"])
            except Exception as e:
                logger.exception("Error processing Google Drive document %s: %s", doc.id, e)
                doc.status = DocumentStatus.FAILED
                doc.error_message = str(e)
                doc.save(update_fields=["status", "error_message"])

        return documents

    def run(self):
        """
        Run the ingestion pipeline for Google Drive content.

        Creates an IngestionPipeline with the appropriate node parser and transformations
        for processing Google Drive content.

        Returns:
            None
        """
        # Get the account from the knowledge base
        account = self.knowledge_base.account
        node_parser = self._get_node_parser()
        logger.info("Node parser: %s", node_parser)

        pipeline = IngestionPipeline(
            transformations=[
                node_parser,
                TagTopicsForContentTransformComponent(account=account),
                self.embed_model,
            ],
            docstore=get_postgres_docstore(
                account_id=account.uuid,
                data_source_id=self.data_source.id,
            ),
            vector_store=get_opensearch_vector_store(),
            docstore_strategy=DocstoreStrategy.UPSERTS_AND_DELETE,
        )

        logger.info("Ingesting documents for data source: %s", self.data_source.id)
        documents = self._get_documents_to_ingest()
        logger.info("Total documents to ingest: %s", len(documents))

        # Add debug logging to inspect document content
        for i, doc in enumerate(documents):
            logger.info("Document %s content length: %s characters", i + 1, len(doc.text))
            logger.info("Document %s metadata: %s", i + 1, doc.metadata)
            # Log a short preview of the content (first 200 chars)
            preview = doc.text[:200] + "..." if len(doc.text) > 200 else doc.text
            logger.info("Document content preview: %s", preview)

        logger.info("Running ingestion pipeline")
        nodes = pipeline.run(
            show_progress=True,
            documents=documents,
        )
        logger.info("Ingestion pipeline completed, total nodes: %s", len(nodes))

        doc_to_nodes = {}
        for node in nodes:
            logger.info("Node: %s", node)
            for rel_type, rel_node in node.relationships.items():
                logger.info("Relationship type: %s, Relationship node: %s", rel_type, rel_node)
                if rel_type == NodeRelationship.SOURCE:
                    logger.info("Relationship type is SOURCE")
                    if rel_node.node_type == ObjectType.DOCUMENT:
                        logger.info("Relationship node is a DOCUMENT")
                        if rel_node.node_id not in doc_to_nodes:
                            logger.info("Adding relationship node to doc_to_nodes")
                            doc_to_nodes[rel_node.node_id] = []
                        doc_to_nodes[rel_node.node_id].append(node)

        self._update_data_source_content(documents, doc_to_nodes)
