import logging
from typing import List

from llama_index.core.ingestion import DocstoreStrategy, IngestionPipeline
from llama_index.core.schema import Document, NodeRelationship, ObjectType
from llama_index.readers.notion import NotionPageReader

from knowledge_bases.models import DataSource
from knowledge_bases.models import Document as DataSourceDocument
from knowledge_bases.services.ingestion_pipeline.base import BaseIngestionPipeline
from services import TagTopicsForContentTransformComponent, get_opensearch_vector_store, get_postgres_docstore
from utils.enum import DocumentStatus

logger = logging.getLogger(__name__)


class TextIngestionPipeline(BaseIngestionPipeline):
    """
    Ingestion pipeline for Text content.
    """

    def __init__(self, data_source: DataSource) -> None:
        """Initialize the Notion ingestion pipeline."""
        super().__init__(data_source)

    def _parse_document(self, doc: DataSourceDocument) -> list[Document]:
        """
        Parse a Text document into a list of nodes.
        """
        document = Document(
            text=doc.raw_content,
            id_=str(doc.id),
            metadata=doc.metadata,
        )

        # Attach proper ID to the document
        document.id_ = f"{doc.data_source_id}_{doc.id}"
        # Add any additional metadata
        document.metadata.update(
            {
                "id": doc.id,
                "data_source_id": self.data_source.id,
                "title": doc.title or "Untitled Notion Document",
                "source": "notion",
            }
        )

        for key, value in doc.metadata.get("permissions", {}).items():
            document.metadata[f"{self.account.uuid}__permissions_{key}"] = value

        return [document]

    def _get_documents_to_ingest(self) -> List[Document]:
        """
        Get the documents to ingest from the data source.
        This method extracts Notion page IDs from document metadata and uses them to fetch content.
        """
        documents = []
        # Get all documents that need to be processed
        documents_to_process = self.data_source.documents.filter(status=DocumentStatus.PENDING, is_active=True)

        if not documents_to_process:
            logger.info("No documents to process")
            return documents

        logger.info("Processing %s documents", documents_to_process.count())

        # Process each document to extract page IDs and fetch content
        for doc in documents_to_process:
            try:
                docs = self._parse_document(doc)
                # Update the document with content from Notion
                doc.raw_content = docs[0].text
                doc.title = docs[0].metadata.get("title", "Untitled Notion Document")
                doc.save(update_fields=["raw_content", "title"])
                documents.extend(docs)
            except Exception as e:
                logger.exception("Error processing Notion document %s: %s", doc.id, e)
                doc.status = DocumentStatus.FAILED
                doc.error_message = str(e)
                doc.save(update_fields=["status", "error_message"])

        return documents

    def run(self):
        """
        Run the ingestion pipeline for Notion content.

        Creates an IngestionPipeline with the appropriate node parser and transformations
        for processing Notion content.

        Returns:
            None
        """
        # Get the account from the knowledge base
        account = self.knowledge_base.account
        node_parser = self._get_node_parser()
        logger.info("Node parser: %s", node_parser)

        pipeline = IngestionPipeline(
            transformations=[
                node_parser,
                TagTopicsForContentTransformComponent(account=account),
                self.embed_model,
            ],
            docstore=get_postgres_docstore(
                account_id=account.uuid,
                data_source_id=self.data_source.id,
            ),
            vector_store=get_opensearch_vector_store(),
            docstore_strategy=DocstoreStrategy.UPSERTS_AND_DELETE,
        )

        logger.info("Ingesting documents for data source: %s", self.data_source.id)
        documents = self._get_documents_to_ingest()
        logger.info("Total documents to ingest: %s", len(documents))

        # Add debug logging to inspect document content
        for i, doc in enumerate(documents):
            logger.info("Document %s content length: %s characters", i + 1, len(doc.text))
            logger.info("Document %s metadata: %s", i + 1, doc.metadata)
            # Log a short preview of the content (first 200 chars)
            preview = doc.text[:200] + "..." if len(doc.text) > 200 else doc.text
            logger.info("Document content preview: %s", preview)

        logger.info("Running ingestion pipeline")
        nodes = pipeline.run(
            show_progress=True,
            documents=documents,
        )
        logger.info("Ingestion pipeline completed, total nodes: %s", len(nodes))

        doc_to_nodes = {}
        for node in nodes:
            logger.info("Node: %s", node)
            for rel_type, rel_node in node.relationships.items():
                logger.info("Relationship type: %s, Relationship node: %s", rel_type, rel_node)
                if rel_type == NodeRelationship.SOURCE:
                    logger.info("Relationship type is SOURCE")
                    if rel_node.node_type == ObjectType.DOCUMENT:
                        logger.info("Relationship node is a DOCUMENT")
                        if rel_node.node_id not in doc_to_nodes:
                            logger.info("Adding relationship node to doc_to_nodes")
                            doc_to_nodes[rel_node.node_id] = []
                        doc_to_nodes[rel_node.node_id].append(node)

        self._update_data_source_content(documents, doc_to_nodes)
