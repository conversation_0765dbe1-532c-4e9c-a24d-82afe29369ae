import logging
from pathlib import Path
from typing import Dict, Any, Optional

from asgiref.sync import sync_to_async
from llama_index.core.schema import Document as LlamaDocument # Avoid name clash with Django Document model
from llama_index.core.workflow import Context # If you want to use LlamaIndex Workflows context

from knowledge_bases.models import DataSource, Document as DataSourceDocument, IngestionJob # Alias Django Document
from knowledge_bases.services.ingestion_pipeline.documents import PDFIngestionPipeline # Import your PDF pipeline
from utils.enum import DocumentStatus, IngestionJobStatus, IngestionJobType

logger = logging.getLogger(__name__)


# --- Main Ingestion Functionality ---
async def trigger_pdf_ingestion(
    ctx: Context, # Can be used if this function is part of a LlamaIndex Workflow
    data_source_id: str,
    pdf_file_path: str,
    permissions: Optional[Dict[str, Any]] = None,
    document_title: Optional[str] = None
) -> Dict[str, Any]:
    """
    Triggers the PDF ingestion process for a specified data source and PDF file.

    Args:
        ctx (Context): The LlamaIndex workflow context (if applicable).
        data_source_id (str): The UUID of the DataSource to ingest into.
        pdf_file_path (str): The absolute path to the PDF file on the server.
        permissions (Optional[Dict[str, Any]]): Optional dictionary of permissions for the document.
        document_title (Optional[str]): Optional title for the document. If not provided,
                                        will be derived from the file path.

    Returns:
        Dict[str, Any]: A dictionary containing job and document IDs, or error information.
    """
    logger.info(f"Initiating PDF ingestion trigger for data_source_id: {data_source_id}, path: {pdf_file_path}")

    permissions = permissions if permissions is not None else {}
    
    try:
        # 1. Retrieve the DataSource instance asynchronously
        data_source = await sync_to_async(DataSource.objects.aget)(uuid=data_source_id)

        if not data_source:
            logger.error(f"DataSource with ID {data_source_id} not found.")
            return {"status": "Failed", "message": f"DataSource {data_source_id} not found."}

        # Derive a title if not provided
        if not document_title:
            document_title = f"Ingested PDF: {Path(pdf_file_path).name}"

        # 2. Create a new Document record in your Django DB
        # This document is marked as PENDING and contains the file_path in its metadata
        new_document = await sync_to_async(DataSourceDocument.objects.create)(
            data_source=data_source,
            title=document_title,
            status=DocumentStatus.PENDING,
            # Store necessary info for the PDFReader in metadata
            metadata={"file_path": pdf_file_path, "permissions": permissions},
            # Slug will be auto-generated by the Document model's save method if it exists
        )
        logger.info(f"Created new DataSourceDocument (ID: {new_document.id}) for ingestion.")

        # 3. Create an IngestionJob record
        ingestion_job = await sync_to_async(IngestionJob.objects.create)(
            datasource=data_source,
            job_type=IngestionJobType.FULL, # Or create a specific PDF type if needed
            status=IngestionJobStatus.PENDING,
            document=new_document, # Link this job to the specific document
        )
        logger.info(f"Created IngestionJob (ID: {ingestion_job.id}) for document (ID: {new_document.id}).")

        # 4. Trigger the Celery task for the ingestion job
        # This will call knowledge_bases.tasks.ingestion.process_ingestion_job.delay(ingestion_job.id)
        # Assuming process_ingestion_job will use the Document.metadata['file_path']
        # to find and process the file via the PDFIngestionPipeline.
        from knowledge_bases.tasks.ingestion import process_ingestion_job # Import locally to avoid circular deps

        process_ingestion_job.delay(ingestion_job.id)
        logger.info(f"Celery task for IngestionJob {ingestion_job.id} triggered successfully.")

        return {
            "status": "Success",
            "message": "PDF ingestion job triggered successfully.",
            "document_id": str(new_document.id),
            "ingestion_job_id": str(ingestion_job.id),
        }

    except DataSource.DoesNotExist:
        logger.error(f"DataSource with ID {data_source_id} not found.")
        return {"status": "Failed", "message": f"DataSource with ID {data_source_id} not found."}
    except FileNotFoundError:
        logger.error(f"PDF file not found at path: {pdf_file_path}")
        return {"status": "Failed", "message": f"PDF file not found at path: {pdf_file_path}. Ensure it's accessible by the Docker container."}
    except Exception as e:
        logger.exception(f"An unexpected error occurred during PDF ingestion trigger: {e}")
        return {"status": "Failed", "message": f"An unexpected error occurred: {str(e)}"}


# --- Functions for Document Processing (Placeholder/Conceptual) ---
# In your current setup, the actual "document processing" happens inside
# PDFIngestionPipeline.run() and PDFReader.load_data().
# These functions would be more relevant if you were doing in-line processing here
# instead of delegating to the pipeline.

# async def process_document_content(file_path: str) -> LlamaDocument:
#     """
#     Reads a PDF file and extracts its raw text, images, and tables.
#     This would typically be done by the PDFReader class.
#     """
#     logger.info(f"Processing raw document content from {file_path}")
#     # Example: reader = PDFReader()
#     # docs = await sync_to_async(reader.load_data)(file_path)
#     # return docs[0] # Assuming one main document is returned
#     raise NotImplementedError("Document processing is handled by PDFReader within the pipeline.")

# --- Functions for Chunking (Placeholder/Conceptual) ---
# Similar to document processing, chunking is handled by the NodeParser
# within the IngestionPipeline.

# async def chunk_document(llama_document: LlamaDocument, strategy: str, config: Dict[str, Any]) -> list:
#     """
#     Breaks down a LlamaDocument into smaller, manageable chunks (Nodes).
#     This would typically be handled by the NodeParser within the IngestionPipeline.
#     """
#     logger.info(f"Chunking document with strategy: {strategy}")
#     # Example:
#     # pipeline = PDFIngestionPipeline(mock_data_source_for_parser_config)
#     # node_parser = pipeline._get_node_parser()
#     # nodes = await sync_to_async(node_parser.get_nodes_from_documents)([llama_document])
#     # return nodes
#     raise NotImplementedError("Chunking is handled by NodeParser within the pipeline.")


# --- Functions for Vector Storage (Placeholder/Conceptual) ---
# The actual storage of vectors (embeddings) happens within the IngestionPipeline.

# async def store_vectors(nodes: list, data_source_id: str) -> None:
#     """
#     Generates embeddings for nodes and stores them in the vector store.
#     This would typically be handled by the embed_model and vector_store within the IngestionPipeline.
#     """
#     logger.info(f"Storing vectors for {len(nodes)} nodes for data_source: {data_source_id}")
#     # Example:
#     # vector_store = get_opensearch_vector_store()
#     # for node in nodes:
#     #     # You'd need embed_model here
#     #     node.embedding = await sync_to_async(embed_model.get_query_embedding)(node.text)
#     #     await sync_to_async(vector_store.add)([node])
#     raise NotImplementedError("Vector storage is handled by the IngestionPipeline.")