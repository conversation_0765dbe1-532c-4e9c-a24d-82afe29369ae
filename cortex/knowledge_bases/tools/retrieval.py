import logging

from asgiref.sync import sync_to_async
from llama_index.core.schema import QueryBundle
from llama_index.core.vector_stores.types import FilterCondition, FilterOperator, MetadataFilter, MetadataFilters
from llama_index.core.workflow import Context

from knowledge_bases.models import KnowledgeBase
from services import get_cohere_rerank_model, get_opensearch_vector_store_index, get_claude_llm
from utils.constants import CLAUDE_V3_5_SONNET_MODEL_ID
from llama_index.core.postprocessor import LLMRerank

logger = logging.getLogger(__name__)


async def search(ctx: Context, user_query: str, topic_ids: list[int]) -> dict:
    """
    Search the knowledge base for the user's question
    """
    logger.info("Searching for query: %s", user_query)
    index = get_opensearch_vector_store_index()
    state = await ctx.get("state")

    similarity_top_k = state["similarity_top_k"]
    logger.info("Similarity top k: %s", similarity_top_k)

    knowledge_base_id = state["knowledge_base_id"]
    logger.info("Knowledge base ID: %s", knowledge_base_id)
    knowledge_base = await KnowledgeBase.objects.aget(uuid=knowledge_base_id)

    if not knowledge_base:
        logger.warning("Knowledge base not found")
        return []

    @sync_to_async
    def get_data_sources():
        return list(knowledge_base.data_sources.all().values_list("id", flat=True))

    data_sources = await get_data_sources()

    logger.info("Initializing filters")
    filters = [
        MetadataFilter(key="data_source_id", operator=FilterOperator(value=FilterOperator.IN), value=data_sources)
    ]
    logger.info("Data source filters initialized")
    if topic_ids:
        logger.info("Adding topic filters")
        filters.append(
            MetadataFilter(
                key=f"{knowledge_base.account_id}__topic_ids",
                operator=FilterOperator(value=FilterOperator.IN),
                value=topic_ids,
            )
        )
        state["topic_ids"] = topic_ids
        await ctx.set("state", state)

    # Permission filters
    if state.get("permissions"):
        logger.info("Adding permissions filters")
        for key, value in state["permissions"].items():
            filters.append(
                MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key=f"{knowledge_base.account_id}__permissions_{key}",
                            operator=FilterOperator(value=FilterOperator.IN),
                            value=value,
                        ),
                        MetadataFilter(
                            key=f"{knowledge_base.account_id}__permissions_{key}",
                            operator=FilterOperator(value=FilterOperator.IS_EMPTY),
                            value=None
                        ),
                    ],
                    condition=FilterCondition.OR,
                )
            )

    logger.info("Initialized filters")
    retriever_args = {
        "similarity_top_k": similarity_top_k,
        "filters": MetadataFilters(filters=filters, condition=FilterCondition.AND),
    }
    logger.info("Retriever args: %s", retriever_args)
    logger.info("Initializing vector retriever")
    vector_retriever = index.as_retriever(**retriever_args)
    logger.info("Vector retriever initialized")
    logger.info("Retrieving documents")
    response = await vector_retriever.aretrieve(user_query)
    logger.info("Documents retrieved")
    logger.debug("Search results: %s", response)

    # Check if rerank is enabled and top_n is provided
    rerank_enabled = state.get("rerank_enabled", False)
    top_n = state.get("rerank_top_n", None)

    if rerank_enabled and top_n is not None and response:
        logger.info("Reranking enabled with top_n=%s", top_n)
        response = await rerank(response, user_query, top_n)
        logger.info("Documents reranked")

    logger.info("Retrieved %s nodes for query: %s", len(response), user_query)

    return [
        {
            "metadata": node.node.metadata,
            "text": node.node.get_text(),
            "score": node.score if hasattr(node, "score") else None,
        }
        for node in response
    ]


async def rerank(nodes: list, user_query: str, top_n: int = 3) -> list:
    """
    Rerank the search results using Cohere's rerank model via AWS Bedrock

    Args:
        nodes: List of NodeWithScore objects from vector search
        user_query: The original user query
        top_n: Number of top results to return after reranking

    Returns:
        List of reranked NodeWithScore objects
    """
    if not nodes:
        logger.warning("No nodes to rerank")
        return nodes

    try:
        logger.info("Reranking %s nodes with top_n=%s using Cohere rerank model", len(nodes), top_n)
        reranker = get_cohere_rerank_model(top_n=min(top_n, len(nodes)))

        query_bundle = QueryBundle(query_str=user_query)

        reranked_nodes = reranker.postprocess_nodes(nodes, query_bundle=query_bundle)
        logger.info("Reranked nodes from %s to %s", len(nodes), len(reranked_nodes))
        return reranked_nodes
    except Exception as e:
        logger.error("Error during reranking: %s", str(e))
        # Return original nodes if reranking fails
        return nodes
    

async def llm_rerank(nodes: list, user_query: str, top_n: int = 3) -> list:
    """
    Rerank the search results using LLM
    """
    if not nodes:
        logger.warning("No nodes to rerank")
        return nodes
    
    try:
        logger.info("Reranking %s nodes with top_n=%s using LLM reranker", len(nodes), top_n)
        reranker = LLMRerank(
            llm=get_claude_llm(),
            choice_batch_size=5,
            top_n=top_n,
        )
        query_bundle = QueryBundle(query_str=user_query)
        reranked_nodes = reranker.postprocess_nodes(nodes, query_bundle=query_bundle)
        logger.info("Reranked nodes from %s to %s", len(nodes), len(reranked_nodes))
        return reranked_nodes
    except Exception as e:
        logger.error("Error during reranking: %s", str(e))
        return nodes
    
