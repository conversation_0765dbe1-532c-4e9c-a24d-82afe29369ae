from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.models import TimeStampedModel


class DocumentChunk(TimeStampedModel):
    """Model representing a chunk of a document for vector storage."""

    # Foreign Keys
    document = models.ForeignKey(
        "Document", on_delete=models.CASCADE, related_name="chunks", help_text=_("The document this chunk belongs to")
    )

    # Fields
    content = models.TextField(help_text=_("The text content of the chunk"))
    chunk_number = models.IntegerField(help_text=_("The sequential number of this chunk within the document"))
    token_count = models.IntegerField(default=0, help_text=_("Number of tokens in this chunk"))
    external_id = models.CharField(
        max_length=255, null=True, blank=True, help_text=_("ID of this chunk in the external vector store")
    )
    page_number = models.IntegerField(
        null=True, blank=True, help_text=_("Page number this chunk starts on (if applicable)")
    )
    metadata = models.JSONField(default=dict, blank=True, null=True, help_text=_("Additional metadata for the chunk"))

    class Meta:
        verbose_name = _("Document Chunk")
        verbose_name_plural = _("Document Chunks")
        ordering = ["document", "chunk_number"]
        # Ensure chunks are unique per document and number
        unique_together = ["document", "chunk_number"]

    def __str__(self):
        return f"{self.document.title} - Chunk {self.chunk_number}"
