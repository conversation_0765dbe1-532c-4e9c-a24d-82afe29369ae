from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.enum import ChunkingStrategy, DataSourceStatus, DataSourceType
from utils.models import TimeStampedModel


class DataSource(TimeStampedModel):
    """Model representing a data source for a knowledge base."""

    # Foreign Keys
    knowledge_base = models.ForeignKey(
        "KnowledgeBase",
        on_delete=models.CASCADE,
        related_name="data_sources",
        help_text=_("The knowledge base this data source belongs to"),
    )

    # Fields
    name = models.CharField(max_length=100, help_text=_("Name of the data source"))
    slug = models.SlugField(max_length=100, help_text=_("Slug of the data source"), null=True, blank=True)
    description = models.TextField(blank=True, null=True, help_text=_("Optional description of the data source"))
    type = models.CharField(max_length=20, choices=DataSourceType.choices, help_text=_("Type of data source"))
    status = models.CharField(
        max_length=20,
        choices=DataSourceStatus.choices,
        default=DataSourceStatus.PENDING,
        help_text=_("Current status of the data source"),
    )
    metadata = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        help_text=_("Configuration settings for the data source (urls, credentials, etc.)"),
    )
    chunking_strategy = models.CharField(
        max_length=50,
        choices=ChunkingStrategy.choices,
        default=ChunkingStrategy.DEFAULT,
        help_text=_("Strategy for chunking documents"),
    )
    chunking_config = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Configuration for the chunking strategy")
    )
    is_active = models.BooleanField(default=True, help_text=_("Whether the data source is active"))
    last_synced_at = models.DateTimeField(null=True, blank=True, help_text=_("When the data source was last synced"))
    last_sync_status = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Status of the last sync operation")
    )
    error_message = models.TextField(
        blank=True, null=True, help_text=_("Error message if the data source processing failed")
    )

    class Meta:
        verbose_name = _("Data Source")
        verbose_name_plural = _("Data Sources")
        ordering = ["-created_at"]
        unique_together = ["knowledge_base", "name", "slug"]

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

    @property
    def credentials(self):
        """
        Get the credentials for the data source.
        """
        return self.metadata.get("credentials", {})
