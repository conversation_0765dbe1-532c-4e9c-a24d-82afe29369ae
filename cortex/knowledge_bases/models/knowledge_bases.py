from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.enum import ChunkingStrategy, EmbeddingModel, KnowledgeBaseStatus, KnowledgeBaseType
from utils.models import CustomModel


class KnowledgeBase(CustomModel):
    """Model representing a knowledge base."""

    # Foreign Keys
    account = models.ForeignKey(
        "accounts.Account",
        on_delete=models.CASCADE,
        related_name="knowledge_bases",
        help_text=_("The account this knowledge base belongs to"),
    )

    # Fields
    name = models.CharField(max_length=100, help_text=_("Name of the knowledge base"))
    slug = models.SlugField(max_length=100, help_text=_("Slug of the knowledge base"))
    description = models.TextField(blank=True, null=True, help_text=_("Description of the knowledge base"))
    type = models.CharField(
        max_length=20,
        choices=KnowledgeBaseType.choices,
        default=KnowledgeBaseType.VECTOR_STORE,
        help_text=_("Type of knowledge base"),
    )
    embedding_model = models.Char<PERSON>ield(
        max_length=50,
        choices=EmbeddingModel.choices,
        default=EmbeddingModel.DEFAULT,
        help_text=_("Embedding model to use"),
    )
    configuration = models.JSO<PERSON>ield(
        default=dict, blank=True, null=True, help_text=_("Configuration for the knowledge base")
    )
    chunking_strategy = models.CharField(
        max_length=50,
        choices=ChunkingStrategy.choices,
        default=ChunkingStrategy.DEFAULT,
        help_text=_("Strategy for chunking documents"),
    )
    chunking_config = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Configuration for the chunking strategy")
    )
    is_active = models.BooleanField(default=True, help_text=_("Whether the knowledge base is active"))
    last_synced_at = models.DateTimeField(
        null=True, blank=True, help_text=_("When the knowledge base was last synced with the external service")
    )
    last_sync_status = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Status of the last sync operation")
    )
    details = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Additional details about the knowledge base")
    )

    class Meta:
        verbose_name = _("Knowledge Base")
        verbose_name_plural = _("Knowledge Bases")
        ordering = ["-created_at"]
        # Ensure knowledge base names are unique per account
        unique_together = ["account", "name", "slug"]

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"
