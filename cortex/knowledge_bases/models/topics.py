from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.models import TimeStampedModel


class Topic(TimeStampedModel):
    """
    Represents a topic that can be assigned to content within a knowledge base.

    Topics are used for categorizing and organizing content, allowing for better
    search and filtering capabilities. Each topic belongs to an account and can
    be applied to various types of content.
    """

    account = models.ForeignKey(
        "accounts.Account",
        on_delete=models.CASCADE,
        related_name="topics_set",
        verbose_name=_("Account"),
        help_text=_("The account this topic belongs to"),
    )

    name = models.CharField(
        max_length=255,
        verbose_name=_("Topic Name"),
        help_text=_("Name of the topic"),
    )

    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_("Description"),
        help_text=_("Description of the topic"),
    )

    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Active"),
        help_text=_("Whether this topic is currently active and available for use"),
    )

    # Optional parent-child relationship for hierarchical topics
    parent = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="children",
        verbose_name=_("Parent Topic"),
        help_text=_("Parent topic if this is a subtopic"),
    )

    class Meta:
        verbose_name = _("Topic")
        verbose_name_plural = _("Topics")
        ordering = ["name"]
        unique_together = ["account", "name"]
        indexes = [
            models.Index(fields=["account", "is_active"]),
            models.Index(fields=["account", "name"]),
        ]

    def __str__(self):
        return self.name

    def to_dict(self):
        """
        Convert the topic to a dictionary format used in transform components.

        Returns:
            dict: Dictionary containing the topic's key information
        """
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
        }
