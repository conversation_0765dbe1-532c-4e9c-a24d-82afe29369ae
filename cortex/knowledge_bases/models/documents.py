from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.enum import ChunkingStrategy, DocumentStatus
from utils.models import TimeStampedModel


class Document(TimeStampedModel):
    """Model representing a document in a data source."""

    # Foreign Keys
    data_source = models.ForeignKey(
        "DataSource",
        on_delete=models.CASCADE,
        related_name="documents",
        help_text=_("The data source this document belongs to"),
    )

    # Fields
    title = models.Char<PERSON>ield(max_length=255, help_text=_("Title of the document"))
    slug = models.SlugField(max_length=100, help_text=_("Slug of the document"), null=True, blank=True)
    raw_content = models.TextField(null=True, blank=True, help_text=_("Raw content of the document"))
    status = models.CharField(
        max_length=20,
        choices=DocumentStatus.choices,
        default=DocumentStatus.PENDING,
        help_text=_("Processing status of the document"),
    )
    is_active = models.<PERSON><PERSON><PERSON><PERSON>ield(default=True, help_text=_("Whether the document is active"))
    chunking_strategy = models.CharField(
        max_length=50,
        choices=ChunkingStrategy.choices,
        default=ChunkingStrategy.DEFAULT,
        help_text=_("Strategy for chunking documents"),
    )
    chunking_config = models.J<PERSON>NField(
        default=dict, blank=True, null=True, help_text=_("Configuration for the chunking strategy")
    )
    last_synced_at = models.DateTimeField(null=True, blank=True, help_text=_("When the document was last synced"))
    last_sync_status = models.JSONField(
        default=dict, blank=True, null=True, help_text=_("Status of the last sync operation")
    )
    error_message = models.TextField(blank=True, null=True, help_text=_("Error message if processing failed"))
    metadata = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        help_text=_("Additional metadata for the document (author, creation date, etc.)"),
    )

    class Meta:
        verbose_name = _("Document")
        verbose_name_plural = _("Documents")
        ordering = ["-created_at"]

    def __str__(self):
        return self.title
