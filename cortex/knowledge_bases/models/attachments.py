from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.models import TimeStampedModel


class Attachment(TimeStampedModel):
    """Model representing a file attachment associated with a document."""

    # Foreign Keys
    document = models.ForeignKey(
        "Document",
        on_delete=models.CASCADE,
        related_name="attachments",
        help_text=_("The document this attachment belongs to"),
    )

    # Fields
    name = models.CharField(max_length=255, help_text=_("Original filename of the attachment"))
    mime_type = models.CharField(max_length=100, help_text=_("MIME type of the attachment"))
    file_extension = models.Char<PERSON>ield(max_length=10, blank=True, null=True, help_text=_("File extension"))
    file_size = models.BigIntegerField(help_text=_("Size of the file in bytes"))
    file_content = models.BinaryField(help_text=_("Binary content of the file"))
    
    # Original source information
    source_url = models.URLField(blank=True, null=True, help_text=_("Original URL where the attachment was downloaded from"))
    source_id = models.CharField(max_length=255, blank=True, null=True, help_text=_("ID of the attachment in the source system"))
    
    # Processing status
    is_processed = models.BooleanField(default=False, help_text=_("Whether the attachment has been processed"))
    processing_error = models.TextField(blank=True, null=True, help_text=_("Error message if processing failed"))
    
    # Metadata
    metadata = models.JSONField(
        default=dict,
        blank=True,
        null=True,
        help_text=_("Additional metadata for the attachment"),
    )

    class Meta:
        verbose_name = _("Attachment")
        verbose_name_plural = _("Attachments")
        ordering = ["-created_at"]
        # Ensure unique attachments per document and source
        unique_together = ["document", "source_id"]

    def __str__(self):
        return f"{self.document.title} - {self.name}"

    @property
    def file_size_mb(self):
        """Return file size in MB."""
        return round(self.file_size / (1024 * 1024), 2)

    def get_file_content(self):
        """Get the binary file content."""
        return bytes(self.file_content)

    def set_file_content(self, content: bytes):
        """Set the binary file content."""
        self.file_content = content
        self.file_size = len(content)
