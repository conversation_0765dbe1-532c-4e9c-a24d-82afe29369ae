from django.db import models
from django.utils.translation import gettext_lazy as _

from utils.enum import IngestionJobStatus, IngestionJobType
from utils.models import TimeStampedModel


class IngestionJob(TimeStampedModel):
    """Model representing an ingestion job for a data source."""

    # Foreign Keys
    datasource = models.ForeignKey(
        "DataSource",
        on_delete=models.CASCADE,
        related_name="ingestion_jobs",
        help_text=_("The data source this ingestion job belongs to"),
    )

    # Fields
    job_type = models.CharField(
        max_length=20,
        choices=IngestionJobType.choices,
        default=IngestionJobType.FULL,
        help_text=_("Type of ingestion job"),
    )
    status = models.CharField(
        max_length=50,
        choices=IngestionJobStatus.choices,
        default=IngestionJobStatus.PENDING,
        help_text=_("Current status of the ingestion job"),
    )
    started_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("When the ingestion job started"),
    )
    finished_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_("When the ingestion job finished"),
    )
    error_msg = models.TextField(
        blank=True,
        null=True,
        help_text=_("Error message if the ingestion job failed"),
    )

    class Meta:
        verbose_name = _("Ingestion Job")
        verbose_name_plural = _("Ingestion Jobs")
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.datasource.name} - {self.job_type} ({self.status})"

    def trigger(self):
        """Trigger the ingestion job."""
        from knowledge_bases.tasks import process_ingestion_job

        # Update the status to processing
        process_ingestion_job.delay(self.id)
