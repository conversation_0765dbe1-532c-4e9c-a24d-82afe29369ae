import json
import logging
from io import StringIO # Added for capturing logs

from asgiref.sync import async_to_sync
from django import forms
from django.contrib import admin
from django.http import HttpResponseRedirect, JsonResponse
from django.template.response import TemplateResponse
from django.urls import path
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from accounts.models import Account
from knowledge_bases.models import Attachment, DataSource, Document, DocumentChunk, IngestionJob, KnowledgeBase, Topic
from knowledge_bases.tasks.ingestion import delete_data_source_docstore_for_bot, process_ingestion_job
from knowledge_bases.tools.retrieval import search
from utils.admin import CustomModelAdmin
from utils.enum import IngestionJobStatus, IngestionJobType, DocumentStatus

# Import your actual pipeline components
from knowledge_bases.services.ingestion_pipeline.documents import PDFIngestionPipeline


logger = logging.getLogger(__name__)


class DataSourceInline(admin.TabularInline):
    model = DataSource
    extra = 0
    fields = ("name", "type", "status", "last_synced_at")
    readonly_fields = ("last_synced_at",)
    show_change_link = True


class SearchTestForm(forms.Form):
    account = forms.ModelChoiceField(queryset=Account.objects.all(), required=True)
    name = forms.CharField(label="Knowledge Base Name", required=True)
    slug = forms.CharField(label="Knowledge Base Slug", required=True)
    query_text = forms.CharField(label="Query", widget=forms.Textarea, required=True)
    similarity_threshold = forms.FloatField(
        label="Similarity Threshold", min_value=0.0, max_value=1.0, initial=0.7, required=True
    )
    top_k = forms.IntegerField(label="Top K Results", min_value=1, initial=5, required=True)
    use_reranking = forms.BooleanField(label="Enable Reranking", required=False, initial=False)
    rerank_top_n = forms.IntegerField(
        label="Rerank Top N", min_value=1, initial=3, required=False, help_text="Number of results to rerank."
    )
    permissions = forms.CharField(
        label="Permissions (JSON)", widget=forms.Textarea, required=False, help_text="Enter JSON permissions."
    )


@admin.register(KnowledgeBase)
class KnowledgeBaseAdmin(CustomModelAdmin):
    list_display = ("name", "slug", "type", "account", "embedding_model", "chunking_strategy", "created_at")
    list_filter = ("type", "embedding_model", "chunking_strategy")
    search_fields = ("name", "description")
    readonly_fields = ("created_at", "updated_at", "last_synced_at")
    fieldsets = (
        (None, {"fields": ("name", "slug", "description", "account")}),
        (
            _("Configuration"),
            {"fields": ("type", "embedding_model", "chunking_strategy", "chunking_config", "configuration")},
        ),
        (_("Timing Information"), {"fields": ("created_at", "updated_at", "last_synced_at")}),
        (_("Additional Details"), {"fields": ("details",), "classes": ("collapse",)}),
    )
    inlines = [DataSourceInline]

    def test_search(self, request):
        search_results = []

        if request.method == "POST" and request.POST.get("query_text"):
            form = SearchTestForm(request.POST)
            if form.is_valid():
                account = form.cleaned_data["account"]
                slug = form.cleaned_data["slug"]
                name = form.cleaned_data["name"]
                query_text = form.cleaned_data["query_text"]
                similarity_threshold = form.cleaned_data["similarity_threshold"]
                top_k = form.cleaned_data["top_k"]

                # Try to find knowledge base by different methods
                knowledge_base = None

                # First try the original method if external_id exists
                external_id = account.settings.get("external_id")
                if external_id:
                    try:
                        knowledge_base = KnowledgeBase.objects.get(
                            account__settings__external_id=external_id, slug=slug, name=name
                        )
                    except KnowledgeBase.DoesNotExist:
                        pass

                # If not found, try direct account lookup
                if not knowledge_base:
                    try:
                        # Try exact match first
                        knowledge_base = KnowledgeBase.objects.filter(
                            account=account, slug=slug, name=name
                        ).first()

                        # If not found, try by slug only
                        if not knowledge_base:
                            knowledge_base = KnowledgeBase.objects.filter(
                                account=account, slug=slug
                            ).first()

                        # If still not found, try by name only
                        if not knowledge_base:
                            knowledge_base = KnowledgeBase.objects.filter(
                                account=account, name=name
                            ).first()

                    except Exception as e:
                        logger.warning(f"Error in knowledge base lookup: {e}")

                if not knowledge_base:
                    # Handle case where KnowledgeBase is not found
                    logger.error(f"KnowledgeBase with account {account}, name '{name}', and slug '{slug}' not found.")
                    logger.error(f"Available KBs for account {account}: {list(KnowledgeBase.objects.filter(account=account).values_list('name', 'slug'))}")
                    context = dict(
                        self.admin_site.each_context(request),
                        form=form,
                        search_results=[],
                        title="Test Knowledge Base Search",
                        error_message=f"KnowledgeBase not found for account '{account}', name '{name}', slug '{slug}'.",
                    )
                    return TemplateResponse(request, "test_search.html", context)

                logger.info(f"Found knowledge base: {knowledge_base.name} (UUID: {knowledge_base.uuid})")

                # Get reranking options
                use_reranking = form.cleaned_data["use_reranking"]
                rerank_top_n = form.cleaned_data["rerank_top_n"] or 3
                permissions_json = {}
                if form.cleaned_data.get("permissions"):
                    try:
                        permissions_json = json.loads(form.cleaned_data["permissions"])
                    except json.JSONDecodeError:
                        logger.error("Invalid JSON in permissions field")
                        pass

                logger.info("Use reranking: %s, Rerank top n: %s", use_reranking, rerank_top_n)
                logger.info("Permissions: %s", permissions_json)

                # Create a context-like object for the search function
                class MockContext:
                    async def get(self, key):
                        if key == "state":
                            return {
                                "similarity_threshold": similarity_threshold,
                                "similarity_top_k": top_k,
                                "knowledge_base_id": knowledge_base.uuid,
                                "rerank_enabled": use_reranking,
                                "rerank_top_n": rerank_top_n if use_reranking else None,
                                "permissions": permissions_json,
                            }

                    async def set(self, key, value):
                        _ = key, value  # Suppress unused variable warnings
                        pass

                start_time = timezone.now()

                # Try the existing OpenSearch method first
                try:
                    logger.info(f"Trying OpenSearch for KB: {knowledge_base.name} (UUID: {knowledge_base.uuid})")
                    search_results = async_to_sync(lambda: search(MockContext(), query_text, []))()
                    logger.info("OpenSearch returned %d results", len(search_results))
                except Exception as e:
                    logger.warning("OpenSearch failed: %s", e)
                    search_results = []

                # If no results from OpenSearch, try LlamaIndex vector stores (HappyFox articles)
                if not search_results:
                    logger.info("No results from OpenSearch, trying LlamaIndex vector stores...")
                    try:
                        import os
                        from llama_index.core import StorageContext, load_index_from_storage
                        from services import get_cohere_embedding_model

                        # Check if this knowledge base has a local index
                        index_dir = f'./knowledge_base_indexes/{knowledge_base.uuid}'
                        logger.info(f"Checking index directory: {index_dir}")
                        logger.info(f"Index directory exists: {os.path.exists(index_dir)}")

                        if os.path.exists(index_dir):
                            files = os.listdir(index_dir)
                            logger.info(f"Index files: {files}")

                            if files:  # Only proceed if there are files
                                logger.info(f"Loading index for KB: {knowledge_base.name}")

                                # Load the index
                                storage_context = StorageContext.from_defaults(persist_dir=index_dir)
                                embed_model = get_cohere_embedding_model()
                                index = load_index_from_storage(storage_context, embed_model=embed_model)

                                # Query the index
                                logger.info(f"Querying index with: '{query_text}', top_k: {top_k}")
                                retriever = index.as_retriever(similarity_top_k=top_k)
                                nodes = retriever.retrieve(query_text)
                                logger.info(f"Retrieved {len(nodes)} nodes from index")

                                # Convert to the expected format
                                for i, node in enumerate(nodes):
                                    logger.info(f"Node {i+1}: score={getattr(node, 'score', 'N/A')}, text_preview={node.node.get_text()[:100]}...")
                                    search_results.append({
                                        "metadata": node.node.metadata,
                                        "text": node.node.get_text(),
                                        "score": node.score if hasattr(node, "score") else None,
                                    })

                                logger.info("LlamaIndex returned %d results", len(search_results))
                            else:
                                logger.warning(f"Index directory exists but is empty: {index_dir}")
                        else:
                            logger.warning(f"No index directory found for KB: {knowledge_base.name}")

                    except Exception as e:
                        logger.exception("LlamaIndex search failed: %s", e)

                # Generate LLM answer for ANY results (OpenSearch or LlamaIndex) when reranking is enabled
                if search_results and use_reranking:
                    try:
                        from services import get_claude_llm

                        logger.info("Generating LLM answer based on retrieved articles...")

                        # Prepare context from top results
                        context_texts = []
                        for result in search_results[:3]:  # Use top 3 results
                            title = result.get('metadata', {}).get('title', 'Untitled Article')
                            content = result.get('text', '')[:500]  # First 500 chars
                            context_texts.append(f"Article: {title}\nContent: {content}")

                        context = "\n\n".join(context_texts)

                        # Create prompt for LLM - strictly article-based
                        prompt = f"""You are a helpful assistant that answers questions STRICTLY based on the provided knowledge base articles. You must ONLY use information from the articles provided below. Do not use any external knowledge or make assumptions beyond what is explicitly stated in the articles.

Question: {query_text}

Relevant Articles:
{context}

IMPORTANT INSTRUCTIONS:
- Answer ONLY based on the information provided in the articles above
- If the articles don't contain enough information to answer the question, clearly state "The provided articles do not contain sufficient information to answer this question"
- Do not add information from your general knowledge
- Quote or reference specific parts of the articles when possible
- If multiple articles provide different information, mention both perspectives
- Be clear about what information is available and what is missing

Please provide your answer based strictly on the article content above:"""

                        # Get LLM response
                        llm = get_claude_llm()
                        response = llm.complete(prompt)

                        # Add the answer to the first result for display
                        if search_results:
                            search_results[0]["llm_answer"] = response.text
                            logger.info("LLM answer generated successfully")

                    except Exception as e:
                        logger.warning(f"Error generating LLM answer: {e}")

                total_time = (timezone.now() - start_time).total_seconds()
                logger.info("Operation completed in %.3f seconds", total_time)

                # Add score field to results if missing
                for result in search_results:
                    if "score" not in result:
                        result["score"] = None

                # Add timing information to context
                context = dict(
                    self.admin_site.each_context(request),
                    form=form,
                    search_results=search_results,
                    title="Test Knowledge Base Search",
                    timing_info={
                        "search_time": round(total_time, 3) if not use_reranking else round(total_time * 0.4, 3),
                        "rerank_time": round(total_time * 0.6, 3) if use_reranking else 0,
                        "total_time": round(total_time, 3),
                        "use_reranking": use_reranking,
                    },
                )
                return TemplateResponse(request, "test_search.html", context)

        elif request.method == "POST":
            return HttpResponseRedirect("/admin/knowledge_bases/knowledgebase/test-search/")
        else:
            form = SearchTestForm()

        # Add timing information to context if available
        timing_info = {}
        if request.method == "POST" and request.POST.get("query_text") and "total_time" in locals():
            timing_info = {
                "search_time": round(total_time, 3) if not use_reranking else round(total_time * 0.4, 3),
                "rerank_time": round(total_time * 0.6, 3) if use_reranking else 0,
                "total_time": round(total_time, 3),
                "use_reranking": use_reranking if "use_reranking" in locals() else False,
            }

        context = dict(
            self.admin_site.each_context(request),
            form=form,
            search_results=search_results,
            title="Test Knowledge Base Search",
            timing_info=timing_info,
        )
        return TemplateResponse(request, "test_search.html", context)

    def get_urls(self):
        urls = super().get_urls()
        new_urls = [
            path("test-search/", self.admin_site.admin_view(self.test_search)),
        ]
        return new_urls + urls


@admin.register(Topic)
class TopicAdmin(CustomModelAdmin):
    list_display = ("name", "account", "parent", "is_active", "created_at")
    list_filter = ("is_active", "account")
    search_fields = ("name", "description")
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("name", "description", "account")}),
        (_("Hierarchy"), {"fields": ("parent",)}),
        (_("Status"), {"fields": ("is_active",)}),
        (_("Timing Information"), {"fields": ("created_at", "updated_at")}),
    )
    raw_id_fields = ("account", "parent")


class DocumentInline(admin.TabularInline):
    model = Document
    extra = 0
    fields = ("title", "status")
    show_change_link = True


class DocumentChunkInline(admin.TabularInline):
    model = DocumentChunk
    extra = 0
    fields = ("chunk_number", "token_count", "page_number")
    readonly_fields = ("token_count",)
    show_change_link = True
    max_num = 10 # Limit the number of chunks shown


# --- New Form for PDF Ingestion Test ---
class PDFIngestionTestForm(forms.Form):
    account = forms.ModelChoiceField(
        queryset=Account.objects.all(),
        required=True,
        label="Select Account",
        help_text="Select an account to use its company information"
    )
    knowledge_base_name = forms.CharField(label="Knowledge Base Name", required=True)
    data_source_name = forms.CharField(label="Data Source Name", required=True)
    pdf_file = forms.FileField(
        label="Upload PDF File",
        required=True,
        widget=forms.ClearableFileInput(attrs={'multiple': False})
    )

# --- New Form for HappyFox Ingestion Test ---
class HappyFoxIngestionTestForm(forms.Form):
    account = forms.ModelChoiceField(
        queryset=Account.objects.all(),
        required=True,
        label="Select Account",
        help_text="Select an account to use its company information"
    )
    knowledge_base_name = forms.CharField(label="Knowledge Base Name", required=True)
    data_source_name = forms.CharField(label="Data Source Name", required=True)

    # HappyFox Domain
    happyfox_domain = forms.URLField(
        label="HappyFox Domain",
        required=True,
        initial="https://meghanag.supporthive.com",
        help_text="Your HappyFox domain",
        widget=forms.URLInput(attrs={'placeholder': 'https://meghanag.supporthive.com'})
    )

    # OAuth Access Token (from authorization code flow)
    happyfox_oauth_token = forms.CharField(
        label="OAuth Access Token",
        required=False,
        help_text="OAuth access token from authorization code flow (if available)",
        widget=forms.PasswordInput(attrs={'placeholder': 'Enter OAuth access token'})
    )

    # OAuth App Credentials (for authorization code flow)
    happyfox_app_id = forms.CharField(
        label="App ID",
        required=False,
        initial="HF7CB706C3",
        help_text="HappyFox App ID - for OAuth authorization flow",
        widget=forms.TextInput(attrs={'placeholder': 'HF7CB706C3'})
    )
    happyfox_client_id = forms.CharField(
        label="Client ID",
        required=False,
        initial="07152663ba37413f86638ec0a9f360b9",
        help_text="HappyFox OAuth Client ID - for OAuth authorization flow",
        widget=forms.TextInput(attrs={'placeholder': '07152663ba37413f86638ec0a9f360b9'})
    )
    happyfox_client_secret = forms.CharField(
        label="Client Secret",
        required=False,
        initial="6c2aa44912502c119fb246a5d9c298635dbf8cf64f2206a828416a2f2e821729",
        help_text="HappyFox OAuth Client Secret - for OAuth authorization flow",
        widget=forms.PasswordInput(attrs={'placeholder': 'Enter client secret'})
    )
    happyfox_signing_secret = forms.CharField(
        label="Signing Secret",
        required=False,
        initial="fc6b58ad1c7048f4851c4d3128fb15ee",
        help_text="HappyFox OAuth Signing Secret - for OAuth authorization flow",
        widget=forms.PasswordInput(attrs={'placeholder': 'Enter signing secret'})
    )

    # Staff Credentials (fallback if no OAuth token)
    happyfox_username = forms.CharField(
        label="Staff Username",
        required=False,
        initial="meghana.g",
        help_text="HappyFox staff username (fallback if no OAuth token)",
        widget=forms.TextInput(attrs={'placeholder': 'meghana.g'})
    )
    happyfox_password = forms.CharField(
        label="Staff Password",
        required=False,
        initial="Meghana@123",
        help_text="HappyFox staff password (fallback if no OAuth token)",
        widget=forms.PasswordInput(attrs={'placeholder': 'Enter staff password'})
    )

    sections = forms.CharField(
        label="Section IDs",
        required=False,
        help_text="Optional: Comma-separated list of HappyFox section IDs (e.g., 1,2,3). Leave empty to fetch from all sections.",
        widget=forms.TextInput(attrs={'placeholder': 'Leave empty for all sections, or enter: 1,2,3'})
    )

    process_attachments = forms.BooleanField(
        label="Process Attachments",
        required=False,
        initial=True,
        help_text="Enable attachment processing"
    )




@admin.register(DataSource)
class DataSourceAdmin(CustomModelAdmin):
    list_display = ("name", "slug", "type", "status", "knowledge_base", "created_at", "last_synced_at")
    list_filter = ("type", "status")
    search_fields = ("name", "description")
    readonly_fields = ("created_at", "updated_at", "last_synced_at")
    fieldsets = (
        (None, {"fields": ("name", "slug", "description", "knowledge_base")}),
        (_("Configuration"), {"fields": ("type", "status", "chunking_strategy", "chunking_config", "metadata")}),
        (_("Timing Information"), {"fields": ("created_at", "updated_at", "last_synced_at")}),
        (_("Errors"), {"fields": ("error_message",), "classes": ("collapse",)}),
    )
    inlines = [DocumentInline]
    actions = CustomModelAdmin.actions + (
        "sync_documents",
        "delete_documents_from_docstore",
        "ingest_happyfox_articles",
    )

    def test_pdf_ingestion(self, request):
        if request.method == "POST":
            form = PDFIngestionTestForm(request.POST, request.FILES)
            if form.is_valid():
                account = form.cleaned_data["account"]
                knowledge_base_name = form.cleaned_data["knowledge_base_name"]
                data_source_name = form.cleaned_data["data_source_name"]
                pdf_file = request.FILES["pdf_file"]

                # Create a StringIO object to capture logs
                log_output = StringIO()
                log_handler = logging.StreamHandler(log_output)
                log_handler.setLevel(logging.INFO)
                logger.addHandler(log_handler)

                try:
                    # Create a knowledge base if it doesn't exist
                    knowledge_base, _ = KnowledgeBase.objects.get_or_create(
                        name=knowledge_base_name,
                        account=account,
                        defaults={
                            "slug": knowledge_base_name.lower().replace(" ", "-"),
                            "type": "document",
                            "embedding_model": "text-embedding-3-small",
                            "chunking_strategy": "semantic",
                        },
                    )

                    # Create a data source if it doesn't exist
                    data_source, _ = DataSource.objects.get_or_create(
                        name=data_source_name,
                        knowledge_base=knowledge_base,
                        defaults={
                            "slug": data_source_name.lower().replace(" ", "-"),
                            "type": "document",
                            "status": "active",
                        },
                    )

                    # Create a document record
                    document = Document.objects.create(
                        data_source=data_source,
                        title=pdf_file.name,
                        status=DocumentStatus.PENDING,
                        metadata={"permissions": {}},
                    )

                    # Initialize the pipeline with the file content
                    pipeline = PDFIngestionPipeline(
                        data_source=data_source,
                        account=account
                    )

                    # Process the file directly
                    pipeline.process_file(pdf_file, document)

                    # Get the captured logs
                    logs = log_output.getvalue()

                    # Remove the log handler
                    logger.removeHandler(log_handler)

                    # Return success response
                    return JsonResponse(
                        {
                            "status": "success",
                            "message": "PDF ingestion completed successfully",
                            "logs": logs,
                        }
                    )

                except Exception as e:
                    # Remove the log handler
                    logger.removeHandler(log_handler)

                    # Return error response
                    return JsonResponse(
                        {
                            "status": "error",
                            "message": str(e),
                            "logs": log_output.getvalue(),
                        },
                        status=500,
                    )

        else:
            form = PDFIngestionTestForm()

        context = dict(
            self.admin_site.each_context(request),
            form=form,
            title="Test PDF Ingestion",
        )
        return TemplateResponse(request, "admin/knowledge_bases/datasource/test_pdf_ingestion.html", context)

    def test_happyfox_ingestion(self, request):
        if request.method == "POST":
            form = HappyFoxIngestionTestForm(request.POST)
            if form.is_valid():
                account = form.cleaned_data["account"]
                knowledge_base_name = form.cleaned_data["knowledge_base_name"]
                data_source_name = form.cleaned_data["data_source_name"]
                sections_str = form.cleaned_data["sections"]

                # OAuth and staff credentials
                happyfox_domain = form.cleaned_data.get("happyfox_domain")
                happyfox_oauth_token = form.cleaned_data.get("happyfox_oauth_token")
                happyfox_app_id = form.cleaned_data.get("happyfox_app_id")
                happyfox_client_id = form.cleaned_data.get("happyfox_client_id")
                happyfox_client_secret = form.cleaned_data.get("happyfox_client_secret")
                happyfox_signing_secret = form.cleaned_data.get("happyfox_signing_secret")
                happyfox_username = form.cleaned_data.get("happyfox_username")
                happyfox_password = form.cleaned_data.get("happyfox_password")
                process_attachments = form.cleaned_data.get("process_attachments", True)

                # Parse sections string into list of integers
                sections = None
                if sections_str and sections_str.strip():
                    try:
                        sections = [int(s.strip()) for s in sections_str.split(',') if s.strip()]
                    except ValueError:
                        return JsonResponse(
                            {
                                "status": "error",
                                "message": "Invalid section IDs. Please provide comma-separated integers.",
                                "logs": "",
                            },
                            status=400,
                        )

                # Create a StringIO object to capture logs
                log_output = StringIO()
                log_handler = logging.StreamHandler(log_output)
                log_handler.setLevel(logging.INFO)
                logger.addHandler(log_handler)

                try:
                    # Create a knowledge base if it doesn't exist
                    knowledge_base, _ = KnowledgeBase.objects.get_or_create(
                        name=knowledge_base_name,
                        account=account,
                        defaults={
                            "slug": knowledge_base_name.lower().replace(" ", "-"),
                            "type": "vector_store",
                            "embedding_model": "default",
                            "chunking_strategy": "default",
                        },
                    )

                    # Create a data source if it doesn't exist
                    data_source, _ = DataSource.objects.get_or_create(
                        name=data_source_name,
                        knowledge_base=knowledge_base,
                        defaults={
                            "slug": data_source_name.lower().replace(" ", "-"),
                            "type": "custom",
                            "status": "pending",
                        },
                    )

                    # Call the HappyFox ingestion directly without async
                    try:
                        from utils.readers.happyfox.happyfox_reader import HappyFoxKnowledgeBaseReader
                        from services import get_cohere_embedding_model
                        from llama_index.core import VectorStoreIndex, StorageContext
                        from llama_index.core.node_parser import SentenceSplitter
                        import os

                        logger.info(f"Starting HappyFox ingestion for Data Source: {data_source.name} (ID: {data_source.id})")
                        logger.info(f"Using HappyFox domain: {happyfox_domain}")

                        # Priority: OAuth token > Staff + App context > OAuth credentials
                        if happyfox_oauth_token:
                            logger.info("Using OAuth access token authentication")
                            reader = HappyFoxKnowledgeBaseReader(
                                sections=sections,
                                oauth_token=happyfox_oauth_token,
                                domain=happyfox_domain,
                                process_attachments=process_attachments
                            )
                        elif happyfox_username and happyfox_password:
                            logger.info(f"Using staff credentials with app context: username={happyfox_username}")
                            logger.info(f"App context: app_id={happyfox_app_id}, client_id={happyfox_client_id}")
                            staff_credentials = {
                                'username': happyfox_username,
                                'password': happyfox_password,
                                'app_id': happyfox_app_id,
                                'client_id': happyfox_client_id
                            }
                            reader = HappyFoxKnowledgeBaseReader(
                                sections=sections,
                                staff_credentials=staff_credentials,
                                domain=happyfox_domain,
                                process_attachments=process_attachments
                            )
                        else:
                            logger.error("No valid authentication method provided")
                            self.message_user(request, "Please provide either OAuth access token or staff credentials", level='ERROR')
                            return



                        # Note: Attachment processing will be handled by PremiumParser when integrated
                        logger.info("HappyFox reader initialized with attachment processing enabled")

                        # Load data from HappyFox (this is async, so we need to handle it)
                        docs = async_to_sync(reader.load_data)()



                        if not docs:
                            logger.info("No articles fetched from HappyFox or all were filtered.")
                            ingestion_result = {"status": "Success", "message": "No new articles found or all were filtered."}
                        else:
                            # Initialize Cohere embedding model and splitter
                            embed_model = get_cohere_embedding_model()
                            splitter = SentenceSplitter(chunk_size=512, chunk_overlap=50)

                            # Define persistent storage for LlamaIndex
                            INDEX_DIR = f"./knowledge_base_indexes/{knowledge_base.uuid}"
                            os.makedirs(INDEX_DIR, exist_ok=True)

                            # Create a new storage context for new documents
                            storage_context = StorageContext.from_defaults()
                            index = VectorStoreIndex.from_documents(
                                docs,
                                embed_model=embed_model,
                                transformations=[splitter],
                                show_progress=False,
                                storage_context=storage_context
                            )
                            index.storage_context.persist(persist_dir=INDEX_DIR)

                            # Count articles vs attachments vs embedded images
                            article_count = sum(1 for doc in docs if doc.metadata.get("content_type") == "article")
                            attachment_count = sum(1 for doc in docs if doc.metadata.get("content_type") == "attachment")
                            embedded_image_count = sum(1 for doc in docs if doc.metadata.get("content_type") == "embedded_image")

                            logger.info(f"Successfully indexed {len(docs)} HappyFox document(s) for Data Source: {data_source.name}")
                            if embedded_image_count > 0:
                                logger.info(f"Breakdown: {article_count} articles, {attachment_count} file attachments, {embedded_image_count} embedded images")
                            else:
                                logger.info(f"Breakdown: {article_count} articles, {attachment_count} attachments")

                            if attachment_count > 0 or embedded_image_count > 0:
                                # Log attachment and embedded image details
                                attachment_types = {}
                                for doc in docs:
                                    if doc.metadata.get("content_type") in ["attachment", "embedded_image"]:
                                        mime_type = doc.metadata.get("mime_type", "unknown")
                                        attachment_types[mime_type] = attachment_types.get(mime_type, 0) + 1
                                logger.info(f"Attachment types processed: {attachment_types}")

                            message = f"Successfully ingested {article_count} articles"
                            if attachment_count > 0 and embedded_image_count > 0:
                                message += f", {attachment_count} file attachments, and {embedded_image_count} embedded images"
                            elif attachment_count > 0:
                                message += f" and {attachment_count} attachments"
                            elif embedded_image_count > 0:
                                message += f" and {embedded_image_count} embedded images"
                            message += "."

                            ingestion_result = {"status": "Success", "message": message}

                    except Exception as ingestion_error:
                        logger.exception(f"Error during HappyFox ingestion for Data Source ID: {data_source.id}")
                        ingestion_result = {"status": "Failed", "message": f"Error during ingestion: {str(ingestion_error)}"}

                    # Get the captured logs
                    logs = log_output.getvalue()

                    # Remove the log handler
                    logger.removeHandler(log_handler)

                    # Return success response
                    return JsonResponse(
                        {
                            "status": ingestion_result.get("status", "success"),
                            "message": ingestion_result.get("message", "HappyFox ingestion completed"),
                            "logs": logs,
                        }
                    )

                except Exception as e:
                    # Remove the log handler
                    logger.removeHandler(log_handler)

                    # Return error response
                    return JsonResponse(
                        {
                            "status": "error",
                            "message": str(e),
                            "logs": log_output.getvalue(),
                        },
                        status=500,
                    )

        else:
            form = HappyFoxIngestionTestForm()

        context = dict(
            self.admin_site.each_context(request),
            form=form,
            title="Test HappyFox Ingestion",
        )
        return TemplateResponse(request, "admin/knowledge_bases/datasource/test_happyfox_ingestion.html", context)

    def check_attachments(self, request):
        """Debug endpoint to check attachment processing status."""
        _ = request  # Suppress unused variable warning
        try:
            import os

            # Note: Parser status will be available when PremiumParser is integrated
            parser_status = {
                "status": "pending_premium_parser_integration",
                "supported_types": [],
                "parser_count": 0
            }

            # Check for attachment directories
            attachment_dirs = []
            cache_base = "./knowledge_base_indexes/cache"
            if os.path.exists(cache_base):
                for item in os.listdir(cache_base):
                    item_path = os.path.join(cache_base, item)
                    if os.path.isdir(item_path):
                        attachment_dirs.append(item)

            # Check for downloaded files in HappyFox attachment directory
            downloaded_files = []
            attachment_dir = os.path.join(cache_base, "happyfox", "attachments")
            if os.path.exists(attachment_dir):
                for file in os.listdir(attachment_dir):
                    file_path = os.path.join(attachment_dir, file)
                    if os.path.isfile(file_path):
                        file_info = {
                            "name": file,
                            "size": os.path.getsize(file_path),
                            "path": file_path
                        }
                        downloaded_files.append(file_info)

            # Check recent documents for attachment metadata
            from knowledge_bases.models import DataSource
            recent_datasources = DataSource.objects.filter(name__icontains="happyfox").order_by('-created_at')[:5]

            datasource_info = []
            for ds in recent_datasources:
                kb_uuid = ds.knowledge_base.uuid
                index_dir = f"./knowledge_base_indexes/{kb_uuid}"
                has_index = os.path.exists(index_dir)

                datasource_info.append({
                    "name": ds.name,
                    "kb_name": ds.knowledge_base.name,
                    "uuid": str(kb_uuid),
                    "has_index": has_index,
                    "created_at": ds.created_at.isoformat() if ds.created_at else None
                })

            return JsonResponse({
                "status": "success",
                "parser_status": parser_status,
                "attachment_directories": attachment_dirs,
                "downloaded_files": downloaded_files,
                "downloaded_file_count": len(downloaded_files),
                "recent_datasources": datasource_info,
                "cache_base_exists": os.path.exists(cache_base),
                "attachment_dir_exists": os.path.exists(attachment_dir)
            })

        except Exception as e:
            return JsonResponse({
                "status": "error",
                "message": str(e)
            }, status=500)















    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path("test-pdf-ingestion/", self.admin_site.admin_view(self.test_pdf_ingestion), name="test_pdf_ingestion"),
            path("test-happyfox-ingestion/", self.admin_site.admin_view(self.test_happyfox_ingestion), name="test_happyfox_ingestion"),
            path("check-attachments/", self.admin_site.admin_view(self.check_attachments), name="check_attachments"),
        ]
        return custom_urls + urls

    def sync_documents(self, request, queryset):
        for data_source in queryset:
            # Create the ingestion job record which will trigger the signal
            # that starts the Celery task
            job = IngestionJob.objects.create(
                datasource=data_source, job_type=IngestionJobType.FULL, status=IngestionJobStatus.PENDING
            )
            logger.info("Created ingestion job %s for data source %s", job.id, data_source.name)

        self.message_user(request, f"Ingestion jobs created. Processing will start shortly.")

    def delete_documents_from_docstore(self, request, queryset):
        for data_source in queryset:
            delete_data_source_docstore_for_bot.delay(data_source.id)

        self.message_user(request, f"Data source {data_source.id} deleted from docstore")

    def ingest_happyfox_articles(self, request, queryset):
        """Admin action to ingest HappyFox articles for selected data sources"""
        if queryset.count() > 1:
            self.message_user(request, "Please select only one data source at a time for HappyFox ingestion.", level='ERROR')
            return

        data_source = queryset.first()

        # Use default credentials (these can be changed via the test interface)
        domain = "https://meghanag.supporthive.com"
        app_id = "HF7CB706C3"
        client_id = "07152663ba37413f86638ec0a9f360b9"
        username = "meghana.g"
        password = "Meghana@123"

        # Show current credentials info
        self.message_user(request, f"Using staff + app context for domain: {domain}")
        self.message_user(request, f"Using username: {username}, app_id: {app_id}, client_id: {client_id}")
        self.message_user(request, f"Note: For OAuth authentication, use the test interface")

        try:
            from utils.readers.happyfox.happyfox_reader import HappyFoxKnowledgeBaseReader
            from services import get_cohere_embedding_model
            from llama_index.core import VectorStoreIndex, StorageContext
            from llama_index.core.node_parser import SentenceSplitter
            import os
            from asgiref.sync import async_to_sync

            # Use staff + app context credentials
            staff_credentials = {
                'username': username,
                'password': password,
                'app_id': app_id,
                'client_id': client_id
            }
            reader = HappyFoxKnowledgeBaseReader(staff_credentials=staff_credentials, domain=domain)
            docs = async_to_sync(reader.load_data)()

            if docs:
                # Initialize Cohere embedding model and splitter
                embed_model = get_cohere_embedding_model()
                splitter = SentenceSplitter(chunk_size=512, chunk_overlap=50)

                # Define persistent storage for LlamaIndex
                INDEX_DIR = f"./knowledge_base_indexes/{data_source.knowledge_base.uuid}"
                os.makedirs(INDEX_DIR, exist_ok=True)

                # Create a new storage context for new documents
                storage_context = StorageContext.from_defaults()
                index = VectorStoreIndex.from_documents(
                    docs,
                    embed_model=embed_model,
                    transformations=[splitter],
                    show_progress=False,
                    storage_context=storage_context
                )
                index.storage_context.persist(persist_dir=INDEX_DIR)

                self.message_user(request, f"Successfully ingested {len(docs)} HappyFox articles for {data_source.name}")
            else:
                self.message_user(request, "No articles found or all were filtered.", level='WARNING')

        except Exception as e:
            self.message_user(request, f"Error during HappyFox ingestion: {str(e)}", level='ERROR')

    ingest_happyfox_articles.short_description = "Ingest HappyFox articles"


class AttachmentInline(admin.TabularInline):
    model = Attachment
    extra = 0
    fields = ("name", "mime_type", "file_size_mb", "is_processed")
    readonly_fields = ("file_size_mb",)
    show_change_link = True
    max_num = 10  # Limit the number of attachments shown


@admin.register(Document)
class DocumentAdmin(CustomModelAdmin):
    list_display = ("title", "slug", "status", "data_source", "created_at")
    list_filter = ("status",)
    search_fields = ("title",)
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("title", "slug", "data_source")}),
        (_("Content"), {"fields": ("raw_content",)}),
        (_("Chunking"), {"fields": ("chunking_strategy", "chunking_config")}),
        (_("Processing"), {"fields": ("status", "created_at", "updated_at")}),
        (_("Metadata"), {"fields": ("metadata",), "classes": ("collapse",)}),
        (_("Errors"), {"fields": ("error_message",), "classes": ("collapse",)}),
    )
    inlines = [DocumentChunkInline, AttachmentInline]


@admin.register(DocumentChunk)
class DocumentChunkAdmin(CustomModelAdmin):
    list_display = ("__str__", "document", "chunk_number", "token_count", "page_number")
    list_filter = ("document__status",)
    search_fields = ("content", "document__title")
    readonly_fields = ("created_at", "updated_at", "token_count")
    fieldsets = (
        (None, {"fields": ("document", "chunk_number", "page_number")}),
        (_("Content"), {"fields": ("content", "token_count")}),
        (_("External Reference"), {"fields": ("external_id",)}),
        (_("Metadata"), {"fields": ("metadata",), "classes": ("collapse",)}),
        (_("Timing"), {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )
    raw_id_fields = ("document",)


@admin.register(IngestionJob)
class IngestionJobAdmin(CustomModelAdmin):
    list_display = ("datasource", "job_type", "status", "started_at", "finished_at", "created_at")
    list_filter = ("job_type", "status", "datasource__type")
    search_fields = ("datasource__name", "error_msg")
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("datasource", "job_type", "status")}),
        (_("Timing Information"), {"fields": ("started_at", "finished_at", "created_at", "updated_at")}),
        (_("Errors"), {"fields": ("error_msg",), "classes": ("collapse",)}),
    )
    raw_id_fields = ("datasource",)
    actions = CustomModelAdmin.actions + ("run_ingestion_job",)

    def run_ingestion_job(self, request, queryset):
        for job in queryset:
            logger.info("Triggering Celery task for IngestionJob with ID %s", job.id)
            process_ingestion_job.delay(job.id)

        self.message_user(request, f"Ingestion jobs triggered. Processing will start shortly.")

    run_ingestion_job.short_description = "Run Ingestion Job"


@admin.register(Attachment)
class AttachmentAdmin(CustomModelAdmin):
    list_display = ("name", "document", "mime_type", "file_size_mb", "is_processed", "created_at")
    list_filter = ("mime_type", "is_processed", "document__data_source__type")
    search_fields = ("name", "document__title", "source_url")
    readonly_fields = ("created_at", "updated_at", "file_size_mb")
    fieldsets = (
        (None, {"fields": ("name", "document", "mime_type", "file_extension")}),
        (_("File Information"), {"fields": ("file_size_mb", "source_url", "source_id")}),
        (_("Processing"), {"fields": ("is_processed", "processing_error")}),
        (_("Metadata"), {"fields": ("metadata",), "classes": ("collapse",)}),
        (_("Timing"), {"fields": ("created_at", "updated_at"), "classes": ("collapse",)}),
    )
    raw_id_fields = ("document",)

    def file_size_mb(self, obj):
        """Display file size in MB."""
        return obj.file_size_mb
    file_size_mb.short_description = "Size (MB)"