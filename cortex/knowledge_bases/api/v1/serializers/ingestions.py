from rest_framework import serializers


class IngestionSerializer(serializers.Serializer):
    account_external_id = serializers.CharField(required=True)
    knowledge_base_slug = serializers.CharField(required=True)
    knowledge_base_name = serializers.CharField(required=True)
    datasource_slug = serializers.CharField(required=True)
    datasource_name = serializers.CharField(required=True)
    datasource_type = serializers.CharField(required=True)
    title = serializers.CharField(required=True)
    content = serializers.CharField(required=False)
    file_url = serializers.CharField(required=False)
    metadata = serializers.JSONField(required=False, default=dict)

    def validate(self, data):
        """
        Validate that content or file_url is provided.
        """
        if not data.get("content") and not data.get("file_url"):
            raise serializers.ValidationError({"content": "Either content or file_url must be provided."})

        return data
