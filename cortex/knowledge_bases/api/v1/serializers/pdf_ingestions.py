# knowledge_bases/api/v1/serializers/ingestion.py

from rest_framework import serializers


class IngestionSerializer(serializers.Serializer):
    """
    Serializer for triggering PDF ingestion via API.
    Expects details to find the target DataSource and the PDF file path.
    """
    # Identifiers for the account and knowledge base
    account_external_id = serializers.CharField(
        required=True, help_text="External ID of the account."
    )
    knowledge_base_slug = serializers.CharField(
        required=True, help_text="Slug of the knowledge base."
    )
    knowledge_base_name = serializers.CharField(
        required=True, help_text="Name of the knowledge base."
    )

    # Identifiers for the specific DataSource
    datasource_slug = serializers.CharField(
        required=True, help_text="Slug of the data source."
    )
    datasource_name = serializers.CharField(
        required=True, help_text="Name of the data source."
    )
    datasource_type = serializers.CharField(
        required=True, help_text="Type of the data source (e.g., 'PDF'). This must match the existing DataSource."
    )

    # Document details for the PDF being ingested
    title = serializers.Char<PERSON>ield(
        required=False, 
        help_text="Optional: Title for the new document record. If not provided, derived from file_path."
    )
    
    # For PDF ingestion, we expect a file_path on the server
    file_path = serializers.CharField(
        required=True, 
        help_text="Absolute path to the PDF file on the server. E.g., '/app/test_data/sample.pdf'"
    )
    
    # Metadata for the document, including permissions
    metadata = serializers.JSONField(
        required=False, 
        default=dict, 
        help_text="Optional: JSON object for document metadata, e.g., {'permissions': {'read': ['user1', 'groupA']}}."
    )