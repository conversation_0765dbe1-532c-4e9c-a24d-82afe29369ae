from rest_framework import serializers


class RetrievalSerializer(serializers.Serializer):
    query = serializers.CharField(required=True)
    similarity_threshold = serializers.FloatField(required=False, min_value=0.0, max_value=1.0, default=0.7)
    top_k = serializers.IntegerField(required=False, default=5)
    rerank_top_n = serializers.IntegerField(required=False, default=3)
    use_reranking = serializers.BooleanField(required=False, default=False)
    account_external_id = serializers.CharField(required=True)
    knowledge_base_slug = serializers.CharField(required=True)
    knowledge_base_name = serializers.CharField(required=True)
    metadata = serializers.JSONField(required=False, default=dict)
