# knowledge_bases/api/v1/views/ingestions.py

import json
import logging

from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.exceptions import APIException
from rest_framework.views import View

from accounts.models import Account
from knowledge_bases.api.v1.serializers.pdf_ingestions import IngestionSerializer
from knowledge_bases.models import DataSource, KnowledgeBase
from knowledge_bases.tools.ingestion import trigger_pdf_ingestion # Import the ingestion tool function
from asgiref.sync import async_to_sync # Needed to call async trigger_pdf_ingestion from sync view

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch") # Disable CSRF for API POST requests (adjust based on your security setup)
class PDFIngestionView(View):
    """
    API endpoint to trigger PDF ingestion for a specific DataSource and file path.
    """

    async def post(self, request):
        try:
            # 1. Parse request body
            payload = json.loads(request.body)
        except json.JSONDecodeError:
            logger.warning("Ingestion API: Invalid JSON payload received.")
            return JsonResponse({"error": "Invalid JSON payload."}, status=400)

        # 2. Validate request data using the serializer
        serializer = IngestionSerializer(data=payload)
        if not serializer.is_valid():
            logger.warning(f"Ingestion API: Invalid request data: {serializer.errors}")
            return JsonResponse(serializer.errors, status=400)

        data = serializer.validated_data
        
        # --- Authentication and Authorization (Crucial for real APIs) ---
        # In a real API, you would add logic here to:
        # 1. Authenticate the user/API key using Django REST Framework's authentication classes.
        # 2. Authorize if this user/key has permission to ingest into the specified account/knowledge_base/datasource.
        # This example skips these for brevity but they are major security considerations.
        # -----------------------------------------------------------------

        try:
            # 3. Find relevant Django models (Account, KnowledgeBase, DataSource)
            account = await Account.objects.filter(
                settings__external_id=data["account_external_id"]
            ).afirst()
            if not account:
                logger.error(f"Ingestion API: Account with external_id {data['account_external_id']} not found.")
                raise APIException("Account not found.")

            knowledge_base = await KnowledgeBase.objects.filter(
                account=account,
                slug=data["knowledge_base_slug"],
                name=data["knowledge_base_name"],
            ).afirst()
            if not knowledge_base:
                logger.error(f"Ingestion API: Knowledge Base {data['knowledge_base_slug']}/{data['knowledge_base_name']} not found for account {account.id}.")
                raise APIException("Knowledge Base not found.")

            data_source = await DataSource.objects.filter(
                knowledge_base=knowledge_base,
                slug=data["datasource_slug"],
                name=data["datasource_name"],
                type=data["datasource_type"], # Ensure type matches what's sent in payload
            ).afirst()
            
            if not data_source:
                logger.error(f"Ingestion API: Data Source {data['datasource_slug']}/{data['datasource_name']} (type: {data['datasource_type']}) not found for KB {knowledge_base.id}.")
                # For this specific PDF test endpoint, the DataSource must exist and be of PDF type.
                raise APIException("Data Source not found or type mismatch. Please ensure it exists and has the correct type.")

            # 4. Create a mock LlamaIndex Context (as this API endpoint is not part of a LlamaIndex Workflow)
            class MockContext:
                async def get(self, key): return None
                async def set(self, key, value): pass

            # 5. Call the core ingestion tool function
            # The tool function is async, so it must be awaited.
            ingestion_result = await trigger_pdf_ingestion(
                ctx=MockContext(), # Pass the mock context
                data_source_id=str(data_source.uuid), # Pass the UUID of the found DataSource
                pdf_file_path=data["file_path"],
                permissions=data["metadata"].get("permissions", {}), # Extract permissions from metadata
                document_title=data.get("title") # Pass optional title
            )
            
            # 6. Return the result from the ingestion tool
            # Assuming the tool returns a dict with 'status' key.
            return JsonResponse(ingestion_result, status=200 if ingestion_result.get("status") == "Success" else 500)

        except APIException as e:
            # Handle specific API exceptions raised
            return JsonResponse({"error": e.detail}, status=404 if "not found" in e.detail.lower() else 400)
        except Exception as e:
            # Catch any other unexpected errors
            logger.exception("An unexpected error occurred during API ingestion.")
            return JsonResponse({"error": "An internal server error occurred."}, status=500)