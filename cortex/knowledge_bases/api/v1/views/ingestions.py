import json
import logging

from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.views import View

from accounts.models import Account
from knowledge_bases.api.v1.serializers import IngestionSerializer
from knowledge_bases.models import DataSource, Document, IngestionJob, KnowledgeBase
from utils.enum import IngestionJobType

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch")
class IngestionView(View):
    """
    Ingest documents into a knowledge base.
    """

    async def post(self, request):
        # Get the request body
        payload = json.loads(request.body)
        serializer = IngestionSerializer(data=payload, many=True)
        if not serializer.is_valid():
            return JsonResponse(serializer.errors, safe=False, status=400)

        data = serializer.validated_data

        data_source_ids = []
        for doc in data:
            # Get or create the account
            account, _ = await Account.objects.aget_or_create(
                settings__external_id=doc["account_external_id"],
                defaults={"name": doc["account_external_id"]},
            )

            # Get or create the knowledge base
            knowledge_base, _ = await KnowledgeBase.objects.aget_or_create(
                slug=doc["knowledge_base_slug"],
                name=doc["knowledge_base_name"],
                account=account,
            )

            # Get or create the datasource
            datasource, _ = await DataSource.objects.aget_or_create(
                knowledge_base=knowledge_base,
                slug=doc["datasource_slug"],
                name=doc["datasource_name"],
                type=doc["datasource_type"],
            )
            data_source_ids.append(datasource.id)

            # Create the Document
            _ = await Document.objects.acreate(
                data_source=datasource,
                raw_content=doc["content"],
                title=doc["title"],
                metadata=doc["metadata"],
            )

        data_source_ids = list(set(data_source_ids))

        for data_source_id in data_source_ids:
            # Create the ingestion job
            ingestion_job = await IngestionJob.objects.acreate(
                datasource_id=data_source_id,
                job_type=IngestionJobType.FULL,
            )

            # Trigger the ingestion job
            # ingestion_job.trigger()

        return JsonResponse(
            {
                "message": "Ingestion jobs created. Data will be ingested shortly.",
            },
            status=200,
        )
