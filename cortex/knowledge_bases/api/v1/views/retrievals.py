import json
import logging

from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.exceptions import APIException
from rest_framework.views import View

from knowledge_bases.api.v1.serializers import RetrievalSerializer
from knowledge_bases.models import KnowledgeBase
from knowledge_bases.tools import search

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name="dispatch")
class RetrievalView(View):
    """
    Retrieves documents from a knowledge base based on a query.
    """

    async def post(self, request):
        # Get the request body
        payload = json.loads(request.body)
        serializer = RetrievalSerializer(data=payload)
        if not serializer.is_valid():
            return JsonResponse(serializer.errors, status=400)

        data = serializer.validated_data

        # Get the Knowledge Base based on account, slug, and name
        knowledge_base = await KnowledgeBase.objects.filter(
            account__settings__external_id=data["account_external_id"],
            slug=data["knowledge_base_slug"],
            name=data["knowledge_base_name"],
        ).afirst()

        if not knowledge_base:
            raise APIException("Knowledge base not found")

        # Create a context-like object for the search function
        class MockContext:
            async def get(self, key):
                if key == "state":
                    return {
                        "similarity_threshold": data["similarity_threshold"],
                        "similarity_top_k": data["top_k"],
                        "knowledge_base_id": knowledge_base.uuid,
                        "rerank_enabled": data["use_reranking"],
                        "rerank_top_n": data["rerank_top_n"] if data["use_reranking"] else None,
                        "permissions": data["metadata"].get("permissions", {}),
                    }

            async def set(self, key, value):
                pass

        search_results = await search(MockContext(), data["query"], [])
        return JsonResponse(search_results, safe=False, status=200)
