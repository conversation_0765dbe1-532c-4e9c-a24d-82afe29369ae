# Generated by Django 5.2 on 2025-05-08 05:54

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import utils.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DataSource",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("name", models.CharField(help_text="Name of the data source", max_length=100)),
                ("slug", models.SlugField(blank=True, help_text="Slug of the data source", max_length=100, null=True)),
                (
                    "description",
                    models.TextField(blank=True, help_text="Optional description of the data source", null=True),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("web_crawler", "Web Crawler"),
                            ("notion", "Notion"),
                            ("gdrive", "Google Drive"),
                            ("onedrive", "OneDrive"),
                            ("upload", "Direct Upload"),
                            ("custom", "Custom"),
                        ],
                        help_text="Type of data source",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("syncing", "Syncing"),
                        ],
                        default="pending",
                        help_text="Current status of the data source",
                        max_length=20,
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Configuration settings for the data source (urls, credentials, etc.)",
                        null=True,
                    ),
                ),
                (
                    "chunking_strategy",
                    models.CharField(
                        choices=[
                            ("default", "Default Chunking"),
                            ("fixed_size", "Fixed-size Chunking"),
                            ("hierarchical", "Hierarchical Chunking"),
                            ("semantic", "Semantic Chunking"),
                            ("none", "No Chunking"),
                        ],
                        default="default",
                        help_text="Strategy for chunking documents",
                        max_length=50,
                    ),
                ),
                (
                    "chunking_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Configuration for the chunking strategy", null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True, help_text="Whether the data source is active")),
                (
                    "last_synced_at",
                    models.DateTimeField(blank=True, help_text="When the data source was last synced", null=True),
                ),
                (
                    "last_sync_status",
                    models.JSONField(
                        blank=True, default=dict, help_text="Status of the last sync operation", null=True
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True, help_text="Error message if the data source processing failed", null=True
                    ),
                ),
            ],
            options={
                "verbose_name": "Data Source",
                "verbose_name_plural": "Data Sources",
                "ordering": ["-created_at"],
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("title", models.CharField(help_text="Title of the document", max_length=255)),
                ("slug", models.SlugField(blank=True, help_text="Slug of the document", max_length=100, null=True)),
                ("raw_content", models.TextField(blank=True, help_text="Raw content of the document", null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("processed", "Processed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        help_text="Processing status of the document",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True, help_text="Whether the document is active")),
                (
                    "chunking_strategy",
                    models.CharField(
                        choices=[
                            ("default", "Default Chunking"),
                            ("fixed_size", "Fixed-size Chunking"),
                            ("hierarchical", "Hierarchical Chunking"),
                            ("semantic", "Semantic Chunking"),
                            ("none", "No Chunking"),
                        ],
                        default="default",
                        help_text="Strategy for chunking documents",
                        max_length=50,
                    ),
                ),
                (
                    "chunking_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Configuration for the chunking strategy", null=True
                    ),
                ),
                (
                    "last_synced_at",
                    models.DateTimeField(blank=True, help_text="When the document was last synced", null=True),
                ),
                (
                    "last_sync_status",
                    models.JSONField(
                        blank=True, default=dict, help_text="Status of the last sync operation", null=True
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, help_text="Error message if processing failed", null=True),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional metadata for the document (author, creation date, etc.)",
                        null=True,
                    ),
                ),
                (
                    "data_source",
                    models.ForeignKey(
                        help_text="The data source this document belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="knowledge_bases.datasource",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document",
                "verbose_name_plural": "Documents",
                "ordering": ["-created_at"],
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
        migrations.CreateModel(
            name="IngestionJob",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                (
                    "job_type",
                    models.CharField(
                        choices=[("FULL", "Full"), ("INCREMENTAL", "Incremental"), ("REINDEX", "Reindex")],
                        default="FULL",
                        help_text="Type of ingestion job",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("processing", "Processing"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                        ],
                        default="pending",
                        help_text="Current status of the ingestion job",
                        max_length=50,
                    ),
                ),
                ("started_at", models.DateTimeField(blank=True, help_text="When the ingestion job started", null=True)),
                (
                    "finished_at",
                    models.DateTimeField(blank=True, help_text="When the ingestion job finished", null=True),
                ),
                (
                    "error_msg",
                    models.TextField(blank=True, help_text="Error message if the ingestion job failed", null=True),
                ),
                (
                    "datasource",
                    models.ForeignKey(
                        help_text="The data source this ingestion job belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ingestion_jobs",
                        to="knowledge_bases.datasource",
                    ),
                ),
            ],
            options={
                "verbose_name": "Ingestion Job",
                "verbose_name_plural": "Ingestion Jobs",
                "ordering": ["-created_at"],
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
        migrations.CreateModel(
            name="KnowledgeBase",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("uuid", models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ("name", models.CharField(help_text="Name of the knowledge base", max_length=100)),
                ("slug", models.SlugField(help_text="Slug of the knowledge base", max_length=100)),
                ("description", models.TextField(blank=True, help_text="Description of the knowledge base", null=True)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("vector_store", "Vector Store"),
                            ("structured_data", "Structured Data"),
                            ("genai_index", "GenAI Index"),
                        ],
                        default="vector_store",
                        help_text="Type of knowledge base",
                        max_length=20,
                    ),
                ),
                (
                    "embedding_model",
                    models.CharField(
                        choices=[
                            ("default", "Default"),
                            ("cohere_multilingual_embedding", "Cohere Multilingual Embedding"),
                        ],
                        default="default",
                        help_text="Embedding model to use",
                        max_length=50,
                    ),
                ),
                (
                    "configuration",
                    models.JSONField(
                        blank=True, default=dict, help_text="Configuration for the knowledge base", null=True
                    ),
                ),
                (
                    "chunking_strategy",
                    models.CharField(
                        choices=[
                            ("default", "Default Chunking"),
                            ("fixed_size", "Fixed-size Chunking"),
                            ("hierarchical", "Hierarchical Chunking"),
                            ("semantic", "Semantic Chunking"),
                            ("none", "No Chunking"),
                        ],
                        default="default",
                        help_text="Strategy for chunking documents",
                        max_length=50,
                    ),
                ),
                (
                    "chunking_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Configuration for the chunking strategy", null=True
                    ),
                ),
                ("is_active", models.BooleanField(default=True, help_text="Whether the knowledge base is active")),
                (
                    "last_synced_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When the knowledge base was last synced with the external service",
                        null=True,
                    ),
                ),
                (
                    "last_sync_status",
                    models.JSONField(
                        blank=True, default=dict, help_text="Status of the last sync operation", null=True
                    ),
                ),
                (
                    "details",
                    models.JSONField(
                        blank=True, default=dict, help_text="Additional details about the knowledge base", null=True
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        help_text="The account this knowledge base belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="knowledge_bases",
                        to="accounts.account",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="deleted_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_%(class)s_set",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Knowledge Base",
                "verbose_name_plural": "Knowledge Bases",
                "ordering": ["-created_at"],
                "unique_together": {("account", "name")},
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
        migrations.AddField(
            model_name="datasource",
            name="knowledge_base",
            field=models.ForeignKey(
                help_text="The knowledge base this data source belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="data_sources",
                to="knowledge_bases.knowledgebase",
            ),
        ),
        migrations.CreateModel(
            name="DocumentChunk",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("content", models.TextField(help_text="The text content of the chunk")),
                (
                    "chunk_number",
                    models.IntegerField(help_text="The sequential number of this chunk within the document"),
                ),
                ("token_count", models.IntegerField(default=0, help_text="Number of tokens in this chunk")),
                (
                    "external_id",
                    models.CharField(
                        blank=True, help_text="ID of this chunk in the external vector store", max_length=255, null=True
                    ),
                ),
                (
                    "page_number",
                    models.IntegerField(
                        blank=True, help_text="Page number this chunk starts on (if applicable)", null=True
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True, default=dict, help_text="Additional metadata for the chunk", null=True
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        help_text="The document this chunk belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chunks",
                        to="knowledge_bases.document",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document Chunk",
                "verbose_name_plural": "Document Chunks",
                "ordering": ["document", "chunk_number"],
                "unique_together": {("document", "chunk_number")},
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
        migrations.CreateModel(
            name="Topic",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("created_at", models.DateTimeField(auto_now_add=True, verbose_name="created at")),
                ("updated_at", models.DateTimeField(auto_now=True, verbose_name="updated at")),
                ("deleted_at", models.DateTimeField(blank=True, null=True, verbose_name="deleted at")),
                ("name", models.CharField(help_text="Name of the topic", max_length=255, verbose_name="Topic Name")),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Description of the topic", null=True, verbose_name="Description"
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this topic is currently active and available for use",
                        verbose_name="Active",
                    ),
                ),
                (
                    "account",
                    models.ForeignKey(
                        help_text="The account this topic belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics_set",
                        to="accounts.account",
                        verbose_name="Account",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        help_text="Parent topic if this is a subtopic",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="children",
                        to="knowledge_bases.topic",
                        verbose_name="Parent Topic",
                    ),
                ),
            ],
            options={
                "verbose_name": "Topic",
                "verbose_name_plural": "Topics",
                "ordering": ["name"],
                "indexes": [
                    models.Index(fields=["account", "is_active"], name="knowledge_b_account_513af7_idx"),
                    models.Index(fields=["account", "name"], name="knowledge_b_account_247adf_idx"),
                ],
                "unique_together": {("account", "name")},
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
    ]
