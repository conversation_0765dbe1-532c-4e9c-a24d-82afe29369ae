# Generated by Django 5.1.6 on 2025-06-18 11:19

import django.db.models.deletion
import utils.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('knowledge_bases', '0004_alter_datasource_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='created at')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='updated at')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='deleted at')),
                ('name', models.CharField(help_text='Original filename of the attachment', max_length=255)),
                ('mime_type', models.Char<PERSON>ield(help_text='MIME type of the attachment', max_length=100)),
                ('file_extension', models.CharField(blank=True, help_text='File extension', max_length=10, null=True)),
                ('file_size', models.BigIntegerField(help_text='Size of the file in bytes')),
                ('file_content', models.BinaryField(help_text='Binary content of the file')),
                ('source_url', models.URLField(blank=True, help_text='Original URL where the attachment was downloaded from', null=True)),
                ('source_id', models.CharField(blank=True, help_text='ID of the attachment in the source system', max_length=255, null=True)),
                ('is_processed', models.BooleanField(default=False, help_text='Whether the attachment has been processed')),
                ('processing_error', models.TextField(blank=True, help_text='Error message if processing failed', null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata for the attachment', null=True)),
                ('document', models.ForeignKey(help_text='The document this attachment belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='knowledge_bases.document')),
            ],
            options={
                'verbose_name': 'Attachment',
                'verbose_name_plural': 'Attachments',
                'ordering': ['-created_at'],
                'unique_together': {('document', 'source_id')},
            },
            bases=(utils.models.AutoUpdateMixin, models.Model),
        ),
    ]
