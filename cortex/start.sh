#!/bin/sh
set -e

if [ -z "$DJANGO_SUPERUSER_USERNAME" ]
then
    echo "Skipping superuser creation as the environment variable - DJANGO_SUPERUSER_USERNAME is not set"
else
    echo "Creating superuser"
    echo "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.create_superuser('$DJANGO_SUPERUSER_USERNAME', '$DJANGO_SUPERUSER_EMAIL', '$DJANGO_SUPERUSER_PASSWORD')" | python manage.py shell
    echo "Superuser created"
    exit 0;
fi

if [ -n "$CELERY_WORKER" ]
then
    echo "Starting celery worker"
    celery -A cortex worker --autoscale $MAX_WORKERS,$MIN_WORKERS --max-tasks-per-child $MAX_TASKS_PER_WORKER --time-limit $MAX_TIME_LIMIT_PER_TASK -l $DJANGO_LOG_LEVEL
    echo "Celery worker started"
    exit 0;
fi

if [ -n "$RUN_MIGRATIONS" ]
then
    echo "Running Django migrations"
    python manage.py migrate
    echo "Django migrations completed"
fi

if [ -n "$RUN_ASYNC_SERVER" ]
then
    echo "Starting async server"
    gunicorn --bind=0.0.0.0:8000 --reload --workers=4 --worker-class=custom_uvicorn_worker.UvicornWorker --timeout 300 cortex.asgi:application
    echo "Async server shutdown"
    exit 0;
fi

echo "Running Django collectstatic"
python manage.py collectstatic --noinput

gunicorn --bind=0.0.0.0:8000 --workers=4 --threads 4 --timeout 300 cortex.wsgi  --reload
